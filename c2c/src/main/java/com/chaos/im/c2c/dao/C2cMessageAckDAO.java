package com.chaos.im.c2c.dao;


import com.chaos.im.c2c.domain.C2cMessageAckDO;
import com.chaos.keep.alive.common.persistent.dao.BaseDAO;

import java.util.List;

public interface C2cMessageAckDAO extends BaseDAO<C2cMessageAckDO> {

    List<C2cMessageAckDO> listByMemberId(Long memberId);

    void updateAck(Long toId, String memberId, String clientId, Long messageId,Long sequence);

    long count(Long toId, String memberId, String clientId);

    C2cMessageAckDO getLastAck(Long chatId, String memberId, String deviceId);

    Long getLastAckMessageId(Long chatId, String memberId, String deviceId);
}
