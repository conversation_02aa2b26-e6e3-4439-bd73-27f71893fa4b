package com.chaos.im.c2c.api.impl;

import com.chaos.im.c2c.api.domain.FetchQuery;
import com.outstanding.framework.core.ResponseDTO;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import com.chaos.im.c2c.api.C2cOpenApi;
import com.chaos.im.c2c.api.domain.MessageSendReq;
import com.chaos.im.c2c.service.MessageSendService;

import lombok.AllArgsConstructor;

@AllArgsConstructor
@DubboService(version = "1.0.0", interfaceClass = C2cOpenApi.class)
public class C2cOpenApiImpl implements C2cOpenApi {

    @Autowired
    private MessageSendService messageSendService;

    @Override
    public void sendMessage(MessageSendReq req) {
        messageSendService.sendMessage(req);
    }


    @Override
    public ResponseDTO<?> fetch( FetchQuery fetchQuery) {
        return ResponseDTO.creatDTO(messageSendService.fetch(
                fetchQuery.getChatId(),
                fetchQuery.getOperatorNo(),
                fetchQuery.getDeviceId(),
                fetchQuery.getStartMessageId(),
                fetchQuery.getStopMessageId(),
                fetchQuery.getPageSize(),fetchQuery.getCurrent(),fetchQuery.getSort()));
    }
    
}
