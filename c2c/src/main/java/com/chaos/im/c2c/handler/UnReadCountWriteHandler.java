package com.chaos.im.c2c.handler;

import cn.hutool.json.JSONUtil;
import com.chaos.im.c2c.domain.C2cMessageBO;
import com.chaos.keep.alive.common.im.constant.ImConstants;
import com.chaos.keep.alive.common.web.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;


@Slf4j
public class UnReadCountWriteHandler extends MessageWriteHandler {
    private RedissonClient redissonClient;

    private RedisTemplate<String, String> redisTemplate;


    private volatile boolean initialized = false;

    private void initDependency() {
        if (!initialized && SpringContextUtils.getApplicationContext() != null) {
            synchronized (this) {
                if (!initialized) {
                    try {
                        this.redissonClient = SpringContextUtils.getBean(RedissonClient.class);
                        this.redisTemplate = SpringContextUtils.getBean("redisTemplate", RedisTemplate.class);

                        this.initialized = true;
                    } catch (Exception e) {
                        log.error("Failed to initialize dependencies", e);
                        throw new RuntimeException("Failed to initialize UnReadCountWriteHandler dependencies", e);
                    }
                }
            }
        }
    }

    @Override
    public boolean isSupport(C2cMessageBO c2cMessageBO) {
        return c2cMessageBO != null;
    }


    @Override
    public void handleRequest(C2cMessageBO c2cMessageBO) {
        initDependency();
        log.debug("更新未读数:{}", JSONUtil.toJsonStr(c2cMessageBO));

        // 使用统一的方法获取成员列表

        String unReadCountKey = ImConstants.C2C_UNREAD_COUNT_PREFIX + c2cMessageBO.getChatId() + ImConstants.SEPARATOR + c2cMessageBO.getToOperatorNo();
        redissonClient.getAtomicLong(unReadCountKey).incrementAndGet();
    }
}
