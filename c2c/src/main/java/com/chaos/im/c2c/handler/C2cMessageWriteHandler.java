package com.chaos.im.c2c.handler;

import com.chaos.im.c2c.domain.C2cMessageBO;
import com.chaos.keep.alive.common.web.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;

@Slf4j
public class C2cMessageWriteHandler extends MessageWriteHandler {
    private  MongoTemplate mongoTemplate;

    private volatile boolean initialized = false;

    private void initDependency(){
        if (!initialized && SpringContextUtils.getApplicationContext() != null) {
            synchronized (this) {
                if (!initialized) {
                    try {
                        this.mongoTemplate = SpringContextUtils.getBean(MongoTemplate.class);
                        this.initialized = true;
                        log.info("C2gMessageWriteHandler dependencies initialized successfully");
                    } catch (Exception e) {
                        log.error("Failed to initialize dependencies", e);
                    }
                }
            }
        }
    }

    @Override
    public boolean isSupport( C2cMessageBO c2cMessageBO) {
        return c2cMessageBO != null;
    }

    @Override
    public void handleRequest(C2cMessageBO c2cMessageBO) {
        initDependency();
        log.debug("单聊消息写入mongodb");
        mongoTemplate.save(c2cMessageBO);
    }
}
