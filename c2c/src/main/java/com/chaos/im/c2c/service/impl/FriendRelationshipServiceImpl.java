package com.chaos.im.c2c.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.chaos.im.c2c.domain.FriendRelationshipDO;
import com.chaos.im.c2c.mapper.FriendRelationshipMapper;
import com.chaos.im.c2c.service.FriendRelationshipService;
import com.chaos.keep.alive.common.web.dao.MybatisPlusDAOImpl;
import org.springframework.stereotype.Service;

@Service
public class FriendRelationshipServiceImpl extends MybatisPlusDAOImpl<FriendRelationshipMapper, FriendRelationshipDO> implements FriendRelationshipService {
    @Override
    public Integer count(String operatorNo, String friendOperatorNo) {


        QueryWrapper<FriendRelationshipDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operator_no", operatorNo);
        queryWrapper.eq("friend_operator_no", friendOperatorNo);

        return mapper.selectCount(queryWrapper);

    }

    @Override
    public void removeByOperatorNo(String operatorNo, String friendOperatorNo) {

        QueryWrapper<FriendRelationshipDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operator_no", operatorNo);
        queryWrapper.eq("friend_operator_no", friendOperatorNo);

        mapper.delete(queryWrapper);


    }
}
