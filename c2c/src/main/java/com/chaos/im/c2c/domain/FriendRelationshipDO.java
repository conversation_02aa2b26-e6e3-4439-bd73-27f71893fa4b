package com.chaos.im.c2c.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chaos.keep.alive.common.persistent.domain.BaseDO;
import lombok.Getter;
import lombok.Setter;


/**
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("friend_relationship")
public class FriendRelationshipDO extends BaseDO {

    private String operatorNo;

    private String friendOperatorNo;

    private String nickname;
}