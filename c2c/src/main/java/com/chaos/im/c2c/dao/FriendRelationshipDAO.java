package com.chaos.im.c2c.dao;


import com.chaos.im.c2c.domain.FriendRelationshipDO;
import com.chaos.im.c2c.domain.FriendRelationshipQuery;
import com.chaos.keep.alive.common.core.domain.BasePage;
import com.chaos.keep.alive.common.persistent.dao.BaseDAO;

/**
 * <AUTHOR>
 */
public interface FriendRelationshipDAO extends BaseDAO<FriendRelationshipDO> {

    BasePage<FriendRelationshipDO> listByPage(FriendRelationshipQuery query);

    long count(String accountId, String friendId);
}
