package com.chaos.im.c2c.service.impl;

import com.chaos.im.c2c.dao.FriendRelationshipDAO;
import com.chaos.im.c2c.dao.FriendRequestDAO;
import com.chaos.im.c2c.domain.FriendRequestDTO;
import com.chaos.im.c2c.domain.FriendRequestQuery;
import com.chaos.im.c2c.service.FriendRequestService;
import com.chaos.keep.alive.common.core.domain.BasePage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class FriendRequestServiceImpl implements FriendRequestService {

    @Autowired
    private FriendRequestDAO friendRequestDAO;

    @Autowired
    private FriendRelationshipDAO friendRelationshipDAO;


    @Override
    public void submit(FriendRequestDTO friendRequestDTO) {

    }

    @Override
    public BasePage<FriendRequestDTO> list(FriendRequestQuery query) {
        return null;
    }
}
