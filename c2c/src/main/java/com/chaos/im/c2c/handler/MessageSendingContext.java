package com.chaos.im.c2c.handler;

import org.apache.dubbo.common.extension.ExtensionLoader;
import org.springframework.stereotype.Component;

@Component
public class MessageSendingContext {

    public IMessageSendHandler init() {

        MessageSendAckHandler messageAckHandler = (MessageSendAckHandler) ExtensionLoader.getExtensionLoader(IMessageSendHandler.class).getExtension("ack");
        MessageSendPushHandler messagePushHandler =  (MessageSendPushHandler)ExtensionLoader.getExtensionLoader(IMessageSendHandler.class).getExtension("push");
        messageAckHandler.setNext(messagePushHandler);

        return messageAckHandler;
    }
}
