package com.chaos.im.c2c.handler;

import com.alibaba.fastjson.JSONObject;
import com.chaos.im.c2c.domain.C2cMessageBO;
import com.chaos.keep.alive.common.im.domain.MessageSendJsonRequest;
import com.chaos.keep.alive.common.im.domain.MessageSendJsonResponse;
import com.chaos.keep.alive.common.web.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.kafka.core.KafkaTemplate;

@Slf4j
public class MessageSendAckHandler extends MessageSendHandler {

    private String TOPIC_MESSAGE_SEND_RESPONSE;
    private KafkaTemplate<String, String> kafkaTemplate;
    private volatile boolean initialized = false;

    private void initDependencies() {
        if (!initialized && SpringContextUtils.getApplicationContext() != null) {
            synchronized (this) {
                if (!initialized) {
                    try {
                        this.kafkaTemplate = SpringContextUtils.getBean("kafkaTemplate", KafkaTemplate.class);

                        // 获取配置值
                        String[] activeProfiles = SpringContextUtils.getApplicationContext().getEnvironment().getActiveProfiles();
                        String activeProfile = activeProfiles.length > 0 ? activeProfiles[0] : "local";
                        this.TOPIC_MESSAGE_SEND_RESPONSE = "topic-message-send-response-" + activeProfile;

                        this.initialized = true;
                        log.info("MessageSendAckHandler dependencies initialized successfully");
                    } catch (Exception e) {
                        this.TOPIC_MESSAGE_SEND_RESPONSE = "topic-message-send-response-local";
                        log.error("Failed to initialize dependencies", e);
                        throw new RuntimeException("Failed to initialize MessageSendAckHandler dependencies", e);
                    }
                }
            }
        }
    }

    @Override
    public boolean isSupport(MessageSendJsonRequest request) {
        return true;
    }

    @Override
    public void handleRequest( MessageSendJsonRequest request) {
        initDependencies();
        sendMessageSendResponse(request);
    }

    public void sendMessageSendResponse(MessageSendJsonRequest request) {
        initDependencies();
        MessageSendJsonResponse message = new MessageSendJsonResponse();
        message.setChatId(request.getChatId());
        message.setMessageId(request.getMessageId());
        message.setFromOperatorNo(request.getFromOperatorNo());
        message.setToOperatorNo(StringUtils.isEmpty(request.getToOperatorNo()) ? request.getFromOperatorNo() : request.getToOperatorNo());
        message.setChatType(request.getChatType());
        message.setSequence(request.getSequence());
        kafkaTemplate.send(TOPIC_MESSAGE_SEND_RESPONSE, JSONObject.toJSONString(message));
    }

    private C2cMessageBO getC2cMessageBO(MessageSendJsonRequest request) {
        C2cMessageBO c2cMessageBO = new C2cMessageBO();
        c2cMessageBO.setId(request.getMessageId());
        c2cMessageBO.setCategory(request.getCategory());
        c2cMessageBO.setToOperatorNo(request.getToOperatorNo());
        c2cMessageBO.setAppId(request.getToAppId());
        c2cMessageBO.setFromOperatorNo(request.getFromOperatorNo());
        c2cMessageBO.setContent(request.getContent());
        c2cMessageBO.setChatId(request.getChatId());
        c2cMessageBO.setMessageId(request.getChatId() + "-" + request.getMessageId());
        c2cMessageBO.setSequence(request.getSequence());
        c2cMessageBO.setTimestamp(System.currentTimeMillis());
        return c2cMessageBO;
    }

}
