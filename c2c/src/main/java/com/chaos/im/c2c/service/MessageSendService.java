package com.chaos.im.c2c.service;


import com.chaos.im.c2c.api.domain.MessageSendReq;
import com.chaos.im.c2c.domain.C2cMessageAckDO;
import com.chaos.keep.alive.common.im.domain.MessageJsonPush;
import com.chaos.keep.alive.common.im.domain.MessageSendJsonRequest;
import com.outstanding.framework.core.PageInfoDTO;

import java.util.List;
import java.util.Map;

public interface MessageSendService {

    void save(List<MessageSendJsonRequest> requests, String toOperatorNo);

    void updateAck(Long chatId, String memberId, String clientId, Long messageId,Long sequence);

    PageInfoDTO<MessageJsonPush> fetch(Long chatId, String operatorNo, String deviceId, Long startMessageId, Long stopMessageId, int size, int current, Map<String,String> sort);

    Long lastAckMessageId(Long chatId, String operatorNo, String deviceId);

    C2cMessageAckDO lastAck(Long chatId, String operatorNo, String deviceId);

    void sendMessage(MessageSendReq body);


}
