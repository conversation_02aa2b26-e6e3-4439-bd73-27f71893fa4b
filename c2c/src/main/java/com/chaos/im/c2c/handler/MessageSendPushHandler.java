package com.chaos.im.c2c.handler;

import com.alibaba.fastjson.JSONObject;
import com.chaos.keep.alive.common.im.domain.MessageJsonPush;
import com.chaos.keep.alive.common.im.domain.MessageSendJsonRequest;
import com.chaos.keep.alive.common.web.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;

import java.util.Objects;


@Slf4j
public class MessageSendPushHandler extends MessageSendHandler {

    private KafkaTemplate<String, String> kafkaTemplate;
    private String TOPIC_MESSAGE_PUSH;
    private volatile boolean initialized = false;

    private void initDependencies() {
        if (!initialized && SpringContextUtils.getApplicationContext() != null) {
            synchronized (this) {
                if (!initialized) {
                    try {
                        this.kafkaTemplate = SpringContextUtils.getBean("kafkaTemplate", KafkaTemplate.class);

                        // 获取配置值
                        String[] activeProfiles = SpringContextUtils.getApplicationContext().getEnvironment().getActiveProfiles();
                        String activeProfile = activeProfiles.length > 0 ? activeProfiles[0] : "local";
                        this.TOPIC_MESSAGE_PUSH = "topic-message-push-" + activeProfile;

                        this.initialized = true;
                        log.info("MessageSendPushHandler dependencies initialized successfully");
                    } catch (Exception e) {
                        this.TOPIC_MESSAGE_PUSH = "topic-message-push-local";
                        log.error("Failed to initialize dependencies", e);
                        throw new RuntimeException("Failed to initialize MessageSendPushHandler dependencies", e);
                    }
                }
            }
        }
    }


    @Override
    public boolean isSupport( MessageSendJsonRequest request) {
        return true;
    }

    @Override
    public void handleRequest( MessageSendJsonRequest request) {
        initDependencies();
        sendMessagePush(request, request.getToOperatorNo());
    }

    public void sendMessagePush(MessageSendJsonRequest request, String memberId) {
        if (!Objects.equals(memberId, request.getFromOperatorNo())) {
            initDependencies();
            log.debug(request.toJsonStr());
            MessageJsonPush message = new MessageJsonPush();
            message.setMessageId(request.getMessageId());
            message.setFromOperatorNo(request.getFromOperatorNo());
            message.setToOperatorNo(memberId);
            message.setChatId(request.getChatId());
            message.setChatType(request.getChatType());
            message.setContent(request.getContent());
            message.setCategory(request.getCategory());
            message.setSequence(request.getSequence());
            message.setTimestamp(System.currentTimeMillis());
            kafkaTemplate.send(TOPIC_MESSAGE_PUSH, JSONObject.toJSONString(message));
        }
    }
}
