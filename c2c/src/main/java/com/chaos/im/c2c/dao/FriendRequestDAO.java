package com.chaos.im.c2c.dao;


import com.chaos.im.c2c.domain.FriendRequestDO;
import com.chaos.im.c2c.domain.FriendRequestQuery;
import com.chaos.keep.alive.common.core.domain.BasePage;
import com.chaos.keep.alive.common.persistent.dao.BaseDAO;

/**
 * <AUTHOR>
 */
public interface FriendRequestDAO extends BaseDAO<FriendRequestDO> {
    long count(String accountId, String friendId);

    BasePage<FriendRequestDO> list(FriendRequestQuery query);
}
