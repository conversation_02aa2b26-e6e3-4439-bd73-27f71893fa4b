package com.chaos.im.c2c.handler;

import com.chaos.im.c2c.domain.C2cMessageBO;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class MessageWriteHandler implements IMessageWriteHandler {
    protected IMessageWriteHandler next;

    @Override
    public void setNext(IMessageWriteHandler next) {
        this.next = next;
    }

    public void handle(C2cMessageBO c2cMessageBO) {
        if(isSupport(c2cMessageBO)) {
            handleRequest(c2cMessageBO);
        }
        if (next != null) {
            log.debug("next handler is {}", next.getClass().getName());
            next.handle( c2cMessageBO);
        }
    }

    public abstract boolean isSupport(C2cMessageBO c2cMessageBO);

    public abstract void handleRequest(C2cMessageBO c2cMessageBO);
}
