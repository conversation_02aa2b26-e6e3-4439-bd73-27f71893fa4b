package com.chaos.im.c2c.service;


import com.chaos.im.c2c.domain.AddFriendResponse;
import com.chaos.im.c2c.domain.FindFriendResponse;
import com.chaos.im.c2c.domain.FriendRelationshipDTO;
import com.chaos.im.c2c.domain.FriendRelationshipQuery;
import com.chaos.keep.alive.common.core.domain.BasePage;

/**
 * <AUTHOR>
 */
public interface FriendService {
    AddFriendResponse addFriend(String operatorNo, String friendOperatorNo);

    BasePage<FriendRelationshipDTO> listByPage(FriendRelationshipQuery query);

    FindFriendResponse findByOperatorNo(String operatorNo);

    void deleteByOperatorNo(String operatorNo,String friendOperatorNo);
}
