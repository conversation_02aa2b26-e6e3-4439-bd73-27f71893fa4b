package com.chaos.im.c2c.handler;

import com.chaos.keep.alive.common.im.domain.MessageSendJsonRequest;
import org.apache.dubbo.common.extension.SPI;


@SPI
public interface IMessageSendHandler {
    void setNext(IMessageSendHandler next);

    void handle( MessageSendJsonRequest request);

    boolean isSupport(MessageSendJsonRequest request);

    void handleRequest(MessageSendJsonRequest request);
}
