package com.chaos.im.c2c.domain;

import com.chaos.keep.alive.common.core.domain.BaseDomain;
import lombok.Getter;
import lombok.Setter;


/**
 * <AUTHOR>
 */
@Getter
@Setter
public class FriendRequestVO extends BaseDomain {

    private Long id;

    private String username;

    /**
     * 好友昵称
     */
    private String nickname;

    /**
     * 申请内容
     */
    private String content;

    /**
     * 申请状态
     */
    private Integer status;
}