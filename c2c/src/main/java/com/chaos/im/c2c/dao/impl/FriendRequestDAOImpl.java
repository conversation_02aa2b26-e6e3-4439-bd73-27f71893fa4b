package com.chaos.im.c2c.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chaos.im.c2c.dao.FriendRequestDAO;
import com.chaos.im.c2c.domain.FriendRequestDO;
import com.chaos.im.c2c.domain.FriendRequestQuery;
import com.chaos.im.c2c.mapper.FriendRequestMapper;
import com.chaos.keep.alive.common.core.domain.BasePage;
import com.chaos.keep.alive.common.web.dao.MybatisPlusDAOImpl;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public class FriendRequestDAOImpl extends MybatisPlusDAOImpl<FriendRequestMapper, FriendRequestDO> implements FriendRequestDAO {
    @Override
    public long count(String accountId, String friendId) {
        LambdaQueryWrapper<FriendRequestDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FriendRequestDO::getAccountId, accountId);
        queryWrapper.eq(FriendRequestDO::getFriendId, friendId);
        return mapper.selectCount(queryWrapper);
    }

    @Override
    public BasePage<FriendRequestDO> list(FriendRequestQuery query) {
        LambdaQueryWrapper<FriendRequestDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FriendRequestDO::getAccountId, query.getAccountId());
        return listPage(query, queryWrapper);
    }
}
