package com.chaos.im.c2c.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chaos.im.c2c.dao.FriendRelationshipDAO;
import com.chaos.im.c2c.domain.FriendRelationshipDO;
import com.chaos.im.c2c.domain.FriendRelationshipQuery;
import com.chaos.im.c2c.mapper.FriendRelationshipMapper;
import com.chaos.keep.alive.common.core.domain.BasePage;
import com.chaos.keep.alive.common.web.dao.MybatisPlusDAOImpl;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public class FriendRelationshipDAOImpl extends MybatisPlusDAOImpl<FriendRelationshipMapper, FriendRelationshipDO> implements FriendRelationshipDAO {

    @Override
    public BasePage<FriendRelationshipDO> listByPage(FriendRelationshipQuery query) {
        LambdaQueryWrapper<FriendRelationshipDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FriendRelationshipDO::getOperatorNo, query.getOperatorNo());
        return listPage(query, queryWrapper);
    }

    @Override
    public long count(String accountId, String friendId) {
        LambdaQueryWrapper<FriendRelationshipDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FriendRelationshipDO::getOperatorNo, accountId);
        queryWrapper.eq(FriendRelationshipDO::getFriendOperatorNo, friendId);
        return mapper.selectCount(queryWrapper);
    }
}
