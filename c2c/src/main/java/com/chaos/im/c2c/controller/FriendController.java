package com.chaos.im.c2c.controller;

import com.chaos.im.c2c.domain.AddFriendRequestVO;
import com.chaos.im.c2c.domain.FriendRelationshipQuery;
import com.chaos.im.c2c.domain.FriendRelationshipVO;
import com.chaos.im.c2c.service.FriendService;
import com.chaos.keep.alive.common.core.util.BeanCopierUtils;
import com.outstanding.framework.core.ResponseDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/friend")
public class FriendController {

    @Autowired
    private FriendService friendService;


    @PostMapping("/add")
    public ResponseDTO<?> addFriend(@RequestBody AddFriendRequestVO addFriendRequestVO) {
       return ResponseDTO.creatDTO(friendService.addFriend(addFriendRequestVO.getOperatorNo(), addFriendRequestVO.getFriendOperatorNo()));
    }

    @PostMapping("/list")
    public ResponseDTO<?> listByPage(@RequestBody FriendRelationshipQuery query) {
        return ResponseDTO.creatDTO(BeanCopierUtils.convert(friendService.listByPage(query), FriendRelationshipVO.class));
    }

    @GetMapping("/findByOperatorNo")
    public ResponseDTO<?> findByOperatorNo(@RequestParam("operatorNo") String operatorNo){
       return ResponseDTO.creatDTO(friendService.findByOperatorNo(operatorNo));
    }
    @DeleteMapping("/deleteByOperatorNo")
    public ResponseDTO<?> deleteByOperatorNo(@RequestParam("operatorNo") String operatorNo,@RequestParam("friendOperatorNo")  String friendOperatorNo){

        friendService.deleteByOperatorNo(operatorNo,friendOperatorNo);

        return ResponseDTO.creatDTO();
    }

}
