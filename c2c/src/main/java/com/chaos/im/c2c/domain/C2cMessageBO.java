package com.chaos.im.c2c.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = false)
@Data
@Document(collection = "c2c_message")
public class C2cMessageBO implements Serializable {

    @Id
    private Long id;
    private Long chatId;
    private String messageId;
    private String fromOperatorNo;
    private String toOperatorNo;
    private Integer category;
    private String content;
    private String appId;
    private Long sequence;
    private Long timestamp;

}
