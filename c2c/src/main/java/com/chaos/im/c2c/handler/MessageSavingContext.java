package com.chaos.im.c2c.handler;

import org.apache.dubbo.common.extension.ExtensionLoader;
import org.springframework.stereotype.Component;

@Component
public class MessageSavingContext {

    public  IMessageWriteHandler init() {

        LastMessageWriteHandler lastMessageWriteHandler = (LastMessageWriteHandler) ExtensionLoader.getExtensionLoader(IMessageWriteHandler.class).getExtension("lastMsg");
        UnReadCountWriteHandler unReadCountWriteHandler = (UnReadCountWriteHandler) ExtensionLoader.getExtensionLoader(IMessageWriteHandler.class).getExtension("unReadCount");
        C2cMessageWriteHandler c2gMessageWriteHandler = (C2cMessageWriteHandler) ExtensionLoader.getExtensionLoader(IMessageWriteHandler.class).getExtension("c2cMessage");
        lastMessageWriteHandler.setNext(unReadCountWriteHandler);
        unReadCountWriteHandler.setNext(c2gMessageWriteHandler);

        return lastMessageWriteHandler;
    }
}
