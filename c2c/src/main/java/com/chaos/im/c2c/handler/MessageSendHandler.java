package com.chaos.im.c2c.handler;

import com.chaos.keep.alive.common.im.domain.MessageSendJsonRequest;

import java.util.List;

public abstract class MessageSendHandler implements IMessageSendHandler {

    protected IMessageSendHandler next;

    @Override
    public void setNext(IMessageSendHandler next) {
        this.next = next;
    }

    @Override
    public void handle( MessageSendJsonRequest request) {
        if(isSupport(request)) {
            handleRequest( request);
        }
        if (next != null) {
            next.handle(request);
        }
    }
}
