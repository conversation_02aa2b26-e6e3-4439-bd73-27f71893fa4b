package com.chaos.im.c2c.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chaos.im.c2c.dao.C2cMessageAckDAO;
import com.chaos.im.c2c.domain.C2cMessageAckDO;
import com.chaos.im.c2c.mapper.C2cMessageAckMapper;
import com.chaos.keep.alive.common.web.dao.MybatisPlusDAOImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class C2cMessageAckDAOImpl extends MybatisPlusDAOImpl<C2cMessageAckMapper, C2cMessageAckDO> implements C2cMessageAckDAO {

    @Override
    public List<C2cMessageAckDO> listByMemberId(Long memberId) {
        LambdaQueryWrapper<C2cMessageAckDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(C2cMessageAckDO::getMemberId, memberId);
        return mapper.selectList(queryWrapper);
    }

    @Override
    public void updateAck(Long chatId, String memberId, String clientId, Long messageId, Long sequence) {
        LambdaUpdateWrapper<C2cMessageAckDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(C2cMessageAckDO::getLastAckMessageId, messageId);
        updateWrapper.set(C2cMessageAckDO::getSequence, sequence);
        updateWrapper.eq(C2cMessageAckDO::getChatId, chatId);
        updateWrapper.eq(C2cMessageAckDO::getMemberId, memberId);
        updateWrapper.eq(C2cMessageAckDO::getDeviceId, clientId);
        mapper.update(null, updateWrapper);
    }


    @Override
    public long count(Long chatId, String memberId, String clientId) {
        LambdaQueryWrapper<C2cMessageAckDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(C2cMessageAckDO::getChatId, chatId);
        queryWrapper.eq(C2cMessageAckDO::getMemberId, memberId);
        queryWrapper.eq(C2cMessageAckDO::getDeviceId, clientId);
        return mapper.selectCount(queryWrapper);
    }


    @Override
    public C2cMessageAckDO getLastAck(Long chatId, String memberId, String deviceId) {
        LambdaQueryWrapper<C2cMessageAckDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(C2cMessageAckDO::getChatId, chatId);
        queryWrapper.eq(C2cMessageAckDO::getMemberId, memberId);
        queryWrapper.eq(C2cMessageAckDO::getDeviceId, deviceId);
        C2cMessageAckDO c2gMessageAckDO = mapper.selectOne(queryWrapper);

        return c2gMessageAckDO;
    }


    @Override
    public Long getLastAckMessageId(Long chatId, String memberId, String deviceId) {
        C2cMessageAckDO c2cMessageAckDO = getLastAck(chatId, memberId, deviceId);
        if (c2cMessageAckDO != null) {
            return c2cMessageAckDO.getLastAckMessageId();
        } else {
            return 0L;
        }
    }
}
