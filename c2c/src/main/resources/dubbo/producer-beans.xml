<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans-4.1.xsd
    http://code.alibabatech.com/schema/dubbo
    http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <dubbo:service interface="com.chaos.im.c2c.api.C2cApi" class="com.chaos.im.c2c.api.impl.C2cApiImpl" version="1.0.0"/>
    <dubbo:service interface="com.chaos.im.c2c.api.C2cOpenApi" class="com.chaos.im.c2c.api.impl.C2cOpenApiImpl" version="1.0.0"/>



</beans>
