<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans-4.1.xsd http://dubbo.apache.org/schema/dubbo http://dubbo.apache.org/schema/dubbo/dubbo.xsd">

    <dubbo:reference interface="com.chaos.usercenter.api.UserOperatorFacade" id="userOperatorFacade" version="1.0.0" timeout="10000"/>
    <dubbo:reference interface="com.chaos.im.chat.api.ChatApi" id="chatApi" version="1.0.0" timeout="10000"/>
    <dubbo:reference interface="com.chaos.keep.alive.im.id.server.api.IdServerApi" id="idServerApi" version="1.0.0" timeout="10000"/>

</beans>
