package com.chaos.ka.im.c2c.test;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.chaos.im.c2c.C2cApplication;
import com.chaos.im.c2c.api.C2cApi;
import com.chaos.im.c2c.api.domain.LastC2cMessageDTO;
import com.chaos.im.c2c.domain.C2cMessageBO;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = C2cApplication.class)
public class C2cApiTest {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @DubboReference
    private C2cApi c2cApi;

    @Test
    public void test(){
        Long chatId = 111L;
        String operatorNo = "1935170073414590464";
        String lastMessageKey = "IM::C2C_LastMessage"+"::"+ chatId+"::"+operatorNo;

        Map<Object,Object> beanMap = redisTemplate.opsForHash().entries(lastMessageKey);
        C2cMessageBO lastC2cMessageNotMe = BeanUtil.mapToBean(beanMap, C2cMessageBO.class, true);
        System.out.println(JSONUtil.toJsonStr(lastC2cMessageNotMe));

        String unReadCountKey = "IM::C2C_UnReadCount"+"::"+ chatId+"::"+operatorNo;
        Object count = redisTemplate.opsForValue().get(unReadCountKey);
        System.out.println(count);

    }

    @Test
    public void test2(){
        Long chatId = 111L;
        String operatorNo = "1935170073414590464";
//        String lastMessageKey = "IM::C2C_LastMessage"+"::"+ chatId+"::"+operatorNo;
        LastC2cMessageDTO lastC2cMessageDTO = c2cApi.getLastMessageByChatIdAndOperatorNo(chatId,operatorNo,"xxxxxx");
        System.out.println(lastC2cMessageDTO);


    }
}
