package com.chaos.im.chat.api;


import com.chaos.im.chat.domain.ChatDTO;
import com.chaos.im.chat.domain.ChatMemberApiVO;

import java.util.List;

public interface ChatApi {

    void addChat(ChatDTO chatDTO);

    String getPeerIdByChatId(Long chatId);

    void deleteChatMember(Integer type,Long peerId,String memberId);

    void deleteChatByGroupId(Integer type, Long groupId);

    void deleteChatByGroupId(Integer type, Long groupId, String memberId);

    Long getChatIdByGroupId(Long groupId);

    List<ChatMemberApiVO> getChatMemberByOperatorNo(String operatorNo);
}
