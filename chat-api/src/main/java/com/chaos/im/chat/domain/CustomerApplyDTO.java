package com.chaos.im.chat.domain;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Date;

@Getter
@Setter
public class CustomerApplyDTO {
    private String orderNo;

    private Date orderTime;

    private String userName;
    private String userMobile;
    private String headUrl;
    private String merchantNo;
    private String merchantName;
    private String merchantMobile;
    private String merchantHeadUrl;
    private String userOperatorNo;
    private String riderNo;
    private String riderName;
    private String riderHeadUrl;
    private String customerNo;
    private Integer type;
    private String lang;
    private Integer readStatus ;
    private Integer interveneStatus ;
    private Integer handleStatus ;

    private Date applyTime;

    private Long chatId;

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;


}
