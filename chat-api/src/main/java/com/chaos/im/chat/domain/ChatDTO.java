package com.chaos.im.chat.domain;

import com.chaos.keep.alive.common.core.domain.BaseDomain;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ChatDTO extends BaseDomain {

    private String memberId;

    private String peerId;

    private String nickname;

    /**
     * 1-好友
     * 2-群组
     */
    private Integer type;

    private String avatar;

    private String ex;
}
