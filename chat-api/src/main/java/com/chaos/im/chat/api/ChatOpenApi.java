package com.chaos.im.chat.api;

import com.chaos.im.chat.domain.*;
import com.outstanding.framework.core.ResponseDTO;

import java.util.*;

public interface ChatOpenApi {

     ResponseDTO<?> page(PageCustomerApplyReq customerApply);

    /**
     * 根据订单号查询客户申请
     */
     ResponseDTO<List<CustomerChatVO>> getByOrderNo(GetChatByOrderNoReq orderNo) ;


    /**
     * 更新处理状态
     */
     ResponseDTO<?> updateHandleStatus(UpdateOrderApplyStatusReq req) ;

     ResponseDTO<?> updateReadStatus(UpdateOrderApplyStatusReq req);


     ResponseDTO<?> acceptCustomerApply( AcceptApplyReq reqDTO);

    ResponseDTO<List<ApplyCustomersRefDTO>> listCustomerByApplyId(CustomerListReq req);
}
