package com.chaos.im.chat.domain;


import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;

@Data
public class PageCustomerApplyReq implements Serializable {

    private String orderNo;

    private Date orderTime;

    private String userName;
    private String userMobile;
    private String headUrl;
    private String merchantNo;
    private String merchantName;
    private String merchantMobile;
    private String merchantHeadUrl;
    private String userOperatorNo;
    private String riderOperatorNo;
    private String riderName;
    private String riderHeadUrl;
    private String customerNo;
    private Integer type;
    private String lang;
    private Integer readStatus ;
    private Integer interveneStatus ;
    private Integer handleStatus ;

    private Date startApplyTime;
    private Date endApplyTime;

    private Long chatId;

    private Integer current = 1;

    private Integer pageSize = 10;

    private Map<String,String> sort;

}
