package com.chaos.keep.alive.dirver.trajectory.service.impl;

import cn.hutool.json.JSONUtil;
import com.chaos.keep.alive.dirver.trajectory.domain.ReportLocationJsonRequest;
import com.chaos.keep.alive.dirver.trajectory.domain.TrajectoryForRedis;
import com.chaos.keep.alive.dirver.trajectory.domain.entity.DriverTrajectory;
import com.chaos.keep.alive.dirver.trajectory.service.DriverTrajectoryService;
import com.chaos.keep.alive.dirver.trajectory.util.DateConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;

import java.time.*;
import java.util.List;
import java.util.Set;

import static com.chaos.keep.alive.dirver.trajectory.Constant.LAST_POINT_PREFIX;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DriverTrajectoryServiceImpl implements DriverTrajectoryService {

    private static final Integer MESSAGE_FETCH_THRESHOLD = 10;

    private static final int FACTOR = 128;

    private final MongoTemplate mongoTemplate;

    @Autowired
    private RedisTemplate redisTemplate;



    @Override
    public void save(List<ReportLocationJsonRequest> requests, String driverId) {

        for (ReportLocationJsonRequest request : requests) {

            log.info("ReportLocationJsonRequest:{}", JSONUtil.toJsonStr(request));
            DriverTrajectory driverTrajectory = new DriverTrajectory();
            driverTrajectory.setDriverId(request.getDriverId());
            driverTrajectory.setDeviceId(request.getDeviceId());
            driverTrajectory.setAppId(request.getAppId());
            driverTrajectory.setAppNo(request.getAppNo());
            driverTrajectory.setLatitude(request.getLatitude());
            driverTrajectory.setLongitude(request.getLongitude());
            driverTrajectory.setSpeed(request.getSpeed());
            driverTrajectory.setDirection(request.getDirection());
            driverTrajectory.setOrderId(request.getOrderId());

            // 将时间戳转换为Instant对象
            Instant instant = Instant.ofEpochMilli(request.getTimestamp());

            // 使用系统默认时区创建ZonedDateTime对象
            ZonedDateTime zonedDateTime = instant.atZone(ZoneId.systemDefault());

            // 提取不带时区的LocalDateTime对象
            LocalDateTime localDateTime = zonedDateTime.toLocalDateTime();

            driverTrajectory.setTimestamp(localDateTime);
            log.debug("司机位置写入mongodb:{}", JSONUtil.toJsonStr(driverTrajectory));
            mongoTemplate.save(driverTrajectory);


            ZSetOperations<String,TrajectoryForRedis> operations = redisTemplate.opsForZSet();
            Set<ZSetOperations.TypedTuple<TrajectoryForRedis>> trajectoryForRedisSet = operations.reverseRangeWithScores(getKey(driverTrajectory.getDriverId()),0,10);

            ZSetOperations.TypedTuple<TrajectoryForRedis> trajectoryForRedisTuple =  trajectoryForRedisSet.stream().findFirst().orElse(null);
            if(trajectoryForRedisTuple==null){
                TrajectoryForRedis newTrajectory = new TrajectoryForRedis();
                newTrajectory.setLatitude(driverTrajectory.getLatitude());
                newTrajectory.setLongitude(driverTrajectory.getLongitude());
                newTrajectory.setTimestamp(driverTrajectory.getTimestamp());
                newTrajectory.setDriverId(driverTrajectory.getDriverId());
                newTrajectory.setDirection(driverTrajectory.getDirection());
                newTrajectory.setSpeed(driverTrajectory.getSpeed());
                double score = driverTrajectory.getTimestamp().toInstant(ZoneOffset.of("+8")).toEpochMilli();
                operations.add(getKey(driverTrajectory.getDriverId()),newTrajectory, score);
            }else {
                double score = driverTrajectory.getTimestamp().toInstant(ZoneOffset.of("+8")).toEpochMilli();
                double scoreInRedis =trajectoryForRedisTuple.getScore();
                if(scoreInRedis<score){
                    TrajectoryForRedis newTrajectory = new TrajectoryForRedis();
                    newTrajectory.setLatitude(driverTrajectory.getLatitude());
                    newTrajectory.setLongitude(driverTrajectory.getLongitude());
                    newTrajectory.setTimestamp(driverTrajectory.getTimestamp());
                    newTrajectory.setDriverId(driverTrajectory.getDriverId());
                    newTrajectory.setDirection(driverTrajectory.getDirection());
                    newTrajectory.setSpeed(driverTrajectory.getSpeed());
                    operations.add(getKey(driverTrajectory.getDriverId()),newTrajectory, score);
                    operations.removeRangeByScore(getKey(driverTrajectory.getDriverId()),0,scoreInRedis);
                }
            }
        }

    }


    @Override
    public List<DriverTrajectory> fetchDriverTrajectory(String driverId,
                                                                     String orderId,
                                                                     String startTime){

        DateConverter converter = new DateConverter();
        LocalDateTime localDateTime = converter.convert(startTime);

        Criteria criteria = Criteria.where("orderId").is(orderId).and("driverId").is(driverId).and("timestamp").gte(localDateTime);
        Query query = new Query(criteria);

        return mongoTemplate.find(query, DriverTrajectory.class);

    }

    @Override
    public TrajectoryForRedis getDriverLastTrajectory(String driverId) {

        ZSetOperations<String,TrajectoryForRedis> operations = redisTemplate.opsForZSet();
        Set<TrajectoryForRedis> trajectory = operations.reverseRange(getKey(driverId),0,10);

        System.out.println("set:"+trajectory);

        return trajectory.stream().findFirst().orElse(null);
    }

    private static String getKey(String driverId) {
        return LAST_POINT_PREFIX + "-" + driverId;
    }


}
