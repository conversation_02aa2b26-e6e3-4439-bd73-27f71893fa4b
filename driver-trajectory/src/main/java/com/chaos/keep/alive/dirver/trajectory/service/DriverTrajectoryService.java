package com.chaos.keep.alive.dirver.trajectory.service;


import com.chaos.keep.alive.dirver.trajectory.domain.ReportLocationJsonRequest;
import com.chaos.keep.alive.dirver.trajectory.domain.TrajectoryForRedis;
import com.chaos.keep.alive.dirver.trajectory.domain.entity.DriverTrajectory;

import java.util.List;

public interface DriverTrajectoryService {

    void save(List<ReportLocationJsonRequest> requests, String driverId);

    List<DriverTrajectory> fetchDriverTrajectory(String driverId,
                                                              String orderId,
                                                              String startTime);

    TrajectoryForRedis getDriverLastTrajectory(String driverId);
}
