package com.chaos.keep.alive.dirver.trajectory.domain.entity;

import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

public class BaseEntity implements Serializable {

    @Id
    private String id;
    /**
     * 创建时间
     */
    @Field
    @CreatedDate
    private Long createTime;

}
