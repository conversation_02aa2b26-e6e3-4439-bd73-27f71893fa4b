package com.chaos.keep.alive.dirver.trajectory.domain;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;


@Getter
@Setter
public class ReportLocationJsonRequest implements Serializable {
    private Integer bizType;
    private String driverId;
    private String appId;
    private Integer appNo;
    private Long speed;
    private Integer direction;
    private Double  latitude;
    private Double longitude;
    private String deviceId;
    private Long timestamp;
    private String orderId;
}
