package com.chaos.keep.alive.dirver.trajectory;

import com.chaos.keep.alive.common.web.config.FastJsonConfig;
import com.chaos.keep.alive.common.web.config.GlobalExceptionConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Import;


@SpringBootApplication
@Import({FastJsonConfig.class, GlobalExceptionConfig.class})
public class DriverTrajectoryApplication {
    public static void main(String[] args) {
        SpringApplication.run(DriverTrajectoryApplication.class, args);
    }
}
