package com.chaos.keep.alive.dirver.trajectory.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.TimeSeries;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Document(collection = "trajectory")
@TimeSeries(collection="trajectory", timeField = "timestamp", metaField = "driverId")
public class DriverTrajectory extends BaseEntity{
    private String driverId;
    private String appId;
    private Integer appNo;
    private Long speed;
    private Integer direction;
    private Double latitude;
    private Double longitude;
    private String deviceId;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;
    private String orderId;

}
