package com.chaos.keep.alive.dirver.trajectory.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
public class TrajectoryForRedis implements Serializable {

    private static final long serialVersionUID = -5738026917822025278L;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;
    private Double latitude;
    private Double longitude;
    private String driverId;
    private Long speed;
    private Integer direction;
}
