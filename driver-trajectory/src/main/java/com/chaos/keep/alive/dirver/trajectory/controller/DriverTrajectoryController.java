package com.chaos.keep.alive.dirver.trajectory.controller;

import com.chaos.keep.alive.dirver.trajectory.domain.TrajectoryForRedis;
import com.chaos.keep.alive.dirver.trajectory.domain.entity.DriverTrajectory;
import com.chaos.keep.alive.dirver.trajectory.service.DriverTrajectoryService;
import com.outstanding.framework.core.ResponseDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/driver/trajectory")
public class DriverTrajectoryController {

    @Autowired
    private DriverTrajectoryService driverTrajectoryService;


    @GetMapping("/fetch.do")
    public ResponseDTO<List<DriverTrajectory>> fetchDriverTrajectory(@RequestParam("driverId") String driverId,
                                                                     @RequestParam("orderId") String orderId,
                                                                     @RequestParam("startTime")String startTime){

        return ResponseDTO.creatDTO( driverTrajectoryService.fetchDriverTrajectory(driverId,orderId,startTime));

    }

    @GetMapping("/lastTrajectory.do")
    public ResponseDTO<TrajectoryForRedis> getDriverLastTrajectory(@RequestParam("driverId") String driverId){
      return ResponseDTO.creatDTO( driverTrajectoryService.getDriverLastTrajectory(driverId));
    }
}
