package com.chaos.keep.alive.dirver.trajectory.handler;

import com.alibaba.fastjson.JSONObject;
import com.chaos.keep.alive.dirver.trajectory.domain.ReportLocationJsonRequest;
import com.chaos.keep.alive.dirver.trajectory.service.DriverTrajectoryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class MessageHandler {

    @Autowired
    private DriverTrajectoryService messageSendService;

    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(
            5, 20, 10, TimeUnit.SECONDS,
            new SynchronousQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    @KafkaListener(topics = "#{'topic-driver-report-location-'.concat('${spring.profiles.active}')}",groupId = "driver-trajectory", properties = {
            ConsumerConfig.MAX_POLL_RECORDS_CONFIG + "=5000"
    })
    public void handleReportLocation(List<ConsumerRecord<String, String>> records, Acknowledgment ack) {
        // 批量消费kafka消息
        Map<String, List<ReportLocationJsonRequest>> grouped = records.stream()
                .map(record -> JSONObject.parseObject(record.value(), ReportLocationJsonRequest.class))
                .collect(Collectors.groupingBy(ReportLocationJsonRequest::getDriverId));
        for (Map.Entry<String, List<ReportLocationJsonRequest>> entry : grouped.entrySet()) {
            // 把消息放入线程池中执行
            executor.execute(() -> messageSendService.save(entry.getValue(), entry.getKey()));
        }
        ack.acknowledge();
    }
}
