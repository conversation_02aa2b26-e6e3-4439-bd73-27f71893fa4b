#apollo.meta=http://apollo-service-dev-apollo-configservice.lifekh-tool-sit.svc.cluster.local:8080
#apollo.bootstrap.enabled=true
#apollo.bootstrap.namespaces=application,goframework.common
#apollo.bootstrap.eagerLoad.enabled=true
#dubbo.application.name=gonow-driver-trajectory
#dubbo.registry.address=zookeeper://svc-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181
#dubbo.protocol.port=20990
#dubbo.provider.group=chaos
#dubbo.consumer.group=chaos
#env=dev
#logging.path=D://log
#logging.config=classpath:log4j.xml
#mybatis-plus.global-config.banner=false
#mybatis-plus.global-config.db-config.id-type=auto
#mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
#server.port=8094
#server.servlet.context-path=/driver-trajectory
#spring.redis.cluster.nodes=drc-redis-0-0.redis-svc-0.lifekh-tool-sit.svc.cluster.local:6379
#spring.redis.cluster.max-redirects=3
#spring.redis.database=0
#spring.data.mongodb.host=************
#spring.data.mongodb.port=27017
#spring.data.mongodb.database=REPORT_BEHAVIOR
#spring.data.mongodb.username=report_behavior
#spring.data.mongodb.password=report_behavior_2020
#spring.main.banner-mode=off
#spring.main.allow-bean-definition-overriding=true
#spring.application.name=gonow-driver-trajectory
#spring.kafka.producer.bootstrap-servers=************:29092
#spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
#spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer
#spring.kafka.consumer.bootstrap-servers=************:29092
#spring.kafka.consumer.group-id=driver-trajectory
#spring.kafka.consumer.auto-offset-reset=latest
#spring.kafka.consumer.enable-auto-commit=false
#spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
#spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer
#spring.kafka.listener.type=batch
#spring.kafka.listener.ack-mode=manual_immediate
