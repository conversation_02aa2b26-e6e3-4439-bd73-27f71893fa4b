#server:
#  port: 8094
#  servlet:
#    context-path: /driver-trajectory
#spring:
#  redis:
#    cluster:
#      nodes: drc-redis-0-0.redis-svc-0.lifekh-tool-sit.svc.cluster.local:6379
#      max-redirects: 3
#    database: 0
#  data:
#    mongodb:
#      host: ************
#      port: 27017
#      database: REPORT_BEHAVIOR
#      username: report_behavior
#      password: report_behavior_2020
#  main:
#    banner-mode: "off"
#    allow-bean-definition-overriding: true
#  application:
#    name: gonow-driver-trajectory
#  kafka:
#    producer:
#      bootstrap-servers: ************:29092
#      key-serializer: org.apache.kafka.common.serialization.StringSerializer
#      value-serializer: org.apache.kafka.common.serialization.StringSerializer
#    consumer:
#      bootstrap-servers: ************:29092
#      group-id: driver-trajectory
#      auto-offset-reset: latest
#      enable-auto-commit: false
#      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
#      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
#    listener:
#      type: batch
#      ack-mode: manual_immediate
#
#mybatis-plus:
#  global-config:
#    banner: false
#    db-config:
#      id-type: auto
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#dubbo:
#  application:
#    name: gonow-driver-trajectory
#  registry:
#    address: zookeeper://zk-dubbo-0.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181?backup=zk-dubbo-1.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181,zk-dubbo-2.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181
#    #address: zookeeper://zk-dubbo-0.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181?backup=zk-dubbo-1.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181,zk-dubbo-2.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181
#  protocol:
#    port: 20990
#  provider:
#    group: chaos
#  consumer:
#    group: chaos