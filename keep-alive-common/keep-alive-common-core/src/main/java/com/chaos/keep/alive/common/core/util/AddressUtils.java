package com.chaos.keep.alive.common.core.util;


import com.chaos.keep.alive.common.core.domain.address.AddressInstance;
import com.chaos.keep.alive.common.core.domain.address.WsAddressInstance;
import com.chaos.keep.alive.common.core.exception.SystemException;

/**
 * <AUTHOR>
 */
public class AddressUtils {

    public static AddressInstance parseAddress(String address) {
        String[] split = address.split(":");
        if (split.length != 3) {
            throw new SystemException("地址解析错误");
        }
        String serverId = split[0];
        String ip = split[1];
        int port = Integer.parseInt(split[2]);
        return new AddressInstance(serverId, ip, port);
    }

    public static WsAddressInstance parseWsAddress(String address) {
        String[] split = address.split(":");
        if (split.length != 4) {
            throw new SystemException("地址解析错误");
        }
        String serverId = split[0];
        String protocal = split[1];
        String ip = split[2];
        String[] portAndContext =  split[3].split("/");
        int port = Integer.parseInt(portAndContext[0]);
        String context = portAndContext.length > 1 ? portAndContext[1] : "";
        return new WsAddressInstance(serverId,protocal, ip, port,context);
    }
}
