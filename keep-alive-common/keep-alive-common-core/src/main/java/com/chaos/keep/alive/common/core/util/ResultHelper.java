package com.chaos.keep.alive.common.core.util;

import com.outstanding.framework.core.ResponseDTO;

public class ResultHelper {

    public static <T> ResponseDTO<T> ok() {
        ResponseDTO<T> result = ResponseDTO.creatDTO();
        return result;
    }

    public static <T> ResponseDTO<T> ok(String message, T data) {
        ResponseDTO<T> result = getResult(message, null, data);
        return result;
    }

    public static <T> ResponseDTO<T> ok(T data) {
        ResponseDTO<T> result = getResult(null, null, data);
        return result;
    }

    public static <T> ResponseDTO<T> fail(String errorCode, String errorMessage) {
        ResponseDTO<T> result = getResult(errorCode, errorMessage, null);
        return result;
    }

    public static <T> ResponseDTO<T> fail(String errorCode, String errorMessage, T data) {
        ResponseDTO<T> result = getResult(errorCode, errorMessage, data);
        return result;
    }

    private static <T> ResponseDTO<T> getResult(String errorCode, String errorMessage, T data) {
        ResponseDTO<T> result = new ResponseDTO<>();
        result.setRspCd(errorCode);
        result.setRspInf(errorMessage);
        result.setData(data);
        return result;
    }
}
