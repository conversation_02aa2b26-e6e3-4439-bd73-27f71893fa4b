package com.chaos.keep.alive.common.web.config;


import com.chaos.keep.alive.common.core.exception.BusinessException;
import com.chaos.keep.alive.common.core.exception.NotFoundException;
import com.chaos.keep.alive.common.core.util.ResultHelper;
import com.outstanding.framework.core.OutStandingException;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.core.ResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.xml.bind.ValidationException;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionConfig {


    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(value = HttpStatus.OK)
    public ResponseDTO<?> handleBusinessException(BusinessException e) {
        return ResultHelper.fail(String.valueOf(HttpStatus.BAD_REQUEST.value()), e.getMessage());
    }

    @ExceptionHandler(ValidationException.class)
    @ResponseStatus(value = HttpStatus.OK)
    public ResponseDTO<?> handleValidationException(ValidationException e) {
        log.error(e.getMessage(), e);
        return ResultHelper.fail(String.valueOf(HttpStatus.BAD_REQUEST.value()), "参数验证失败");
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(value = HttpStatus.OK)
    public ResponseDTO<?> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        return ResultHelper.fail(String.valueOf(HttpStatus.BAD_REQUEST.value()), "数据验证错误");
    }

    @ExceptionHandler(value = {
            MissingServletRequestParameterException.class,
            IllegalArgumentException.class
    })
    @ResponseStatus(value = HttpStatus.OK)
    public ResponseDTO<?> handleIllegalArgumentException(Exception e) {
        log.error(e.getMessage(), e);
        return ResultHelper.fail(String.valueOf(HttpStatus.BAD_REQUEST.value()), "参数解析失败");
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(value = HttpStatus.OK)
    public ResponseDTO<?> handleHttpRequestMethodNotSupportedException() {
        return ResultHelper.fail(String.valueOf(HttpStatus.METHOD_NOT_ALLOWED.value()), "不支持当前请求方法");
    }

    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    @ResponseStatus(value = HttpStatus.OK)
    public ResponseDTO<?> handleHttpMediaTypeNotSupportedException() {
        return ResultHelper.fail(String.valueOf(HttpStatus.UNSUPPORTED_MEDIA_TYPE.value()), "不支持的媒体类型");
    }

    @ExceptionHandler(NotFoundException.class)
    @ResponseStatus(value = HttpStatus.OK)
    public ResponseDTO<?> handleNotFoundException(NotFoundException e) {
        return ResultHelper.fail(String.valueOf(HttpStatus.NOT_FOUND.value()), e.getMessage());
    }

    @ExceptionHandler(OutStandingException.class)
    @ResponseStatus(value = HttpStatus.OK)
    public ResponseDTO<?> handleOutStandingException(Throwable e) {
        log.error(e.getMessage(), e);

        if(e instanceof PendingException){
            return ResultHelper.fail(String.valueOf(HttpStatus.BAD_REQUEST.value()), e.getMessage());
        }
        return ResultHelper.fail(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()), "服务器错误");
    }

    @ExceptionHandler(Exception.class)
    @ResponseStatus(value = HttpStatus.OK)
    public ResponseDTO<?> handleAllException(Throwable e) {
        log.error(e.getMessage(), e);
        return ResultHelper.fail(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()), "服务器错误");
    }


}
