package com.chaos.keep.alive.common.web.util;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.outstanding.framework.core.PageInfoDTO;

import java.util.ArrayList;
import java.util.List;

public class PageUtil {

    public static <T> PageInfoDTO<T> createPage(IPage<T> page) {
        return createPage(page.getCurrent(), page.getSize(), page.getTotal(), page.getRecords());
    }

    public static <T> PageInfoDTO<T> createPage(IPage<?> page, List<T> records) {
        return createPage(page.getCurrent(), page.getSize(), page.getTotal(), records);
    }

    public static <T> PageInfoDTO<T> createPage(long pageNum, long pageSize, long total, List<T> records) {
        if (records == null) {
            records = new ArrayList<>();
        }
        PageInfoDTO<T> dto = new PageInfoDTO<>();
        dto.setPageNum((int) pageNum);
        dto.setPageSize((int) pageSize);
        dto.setSize(records.size());
        dto.setList(records);
        dto.setTotal(total);
        dto.setPages((int) (dto.getTotal() / dto.getPageSize() + ((dto.getTotal() % dto.getPageSize() == 0) ? 0 : 1)));
        dto.setFirstPage(dto.getPageNum() == 1);
        dto.setLastPage(dto.getPageNum() == dto.getPages() || dto.getPages() == 0);
        dto.setHasPreviousPage(dto.getPageNum() > 1);
        dto.setHasNextPage(dto.getPageNum() < dto.getPages());
        if (dto.getSize() > 0) {
            dto.setStartRow((dto.getPageNum() - 1) * dto.getPageSize() + 1);
            dto.setEndRow(dto.getStartRow() - 1 + dto.getSize());
        }
        return dto;
    }
}
