<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>keep-alive-common-web</artifactId>
    <version>1.0.1</version>
    <packaging>jar</packaging>

    <parent>
        <groupId>com.chaos</groupId>
        <artifactId>keep-alive-common</artifactId>
        <version>1.0.0</version>
    </parent>

    <dependencies>


<!--        <dependency>-->
<!--            <groupId>com.outstanding</groupId>-->
<!--            <artifactId>framework-plugin-monitor</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.outstanding</groupId>
            <artifactId>framework-container-springmvc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chaos</groupId>
            <artifactId>keep-alive-common-core</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.chaos</groupId>
            <artifactId>keep-alive-common-persistent</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.1</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
            <version>${mybatis.plus.version}</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis.plus.version}</version>
        </dependency>

        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>2.3.1</version>
        </dependency>




    </dependencies>
</project>