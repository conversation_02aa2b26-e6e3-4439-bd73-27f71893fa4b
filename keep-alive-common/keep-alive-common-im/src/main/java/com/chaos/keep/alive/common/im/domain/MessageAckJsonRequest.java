package com.chaos.keep.alive.common.im.domain;

import com.chaos.keep.alive.common.core.domain.BaseDomain;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class MessageAckJsonRequest extends BaseDomain {

    private Integer chatType;

    private String deviceId;

    private Long chatId;

    private String toOperatorNo;

    private String operatorNo;

    private Long messageId;

    private Long sequence;
}
