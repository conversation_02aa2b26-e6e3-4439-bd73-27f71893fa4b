package com.chaos.keep.alive.common.im.domain;

import cn.hutool.json.JSONObject;
import com.chaos.keep.alive.common.protobuf.Command;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class JsonCommand {

    private String appSdkVersion;

    private Integer messageType;

    private Integer bizType;

    private Long timestamp;

    private String operatorNo;

    private String deviceId;

    private String appId;

    private String body;


    public static JsonCommand convert(Command command) {
        JsonCommand jsonCommand = new JsonCommand();
        jsonCommand.setBizType(command.getBizType());
        jsonCommand.setOperatorNo(command.getOperatorNo());
        jsonCommand.setDeviceId(command.getDeviceId());
        jsonCommand.setAppId(command.getAppId());
        jsonCommand.setTimestamp(command.getTimestamp());
        jsonCommand.setMessageType(command.getMessageType());
        jsonCommand.setAppSdkVersion(command.getAppSdkVersion());
        jsonCommand.setBody(command.getBody().toStringUtf8());
        return jsonCommand;
    }
}
