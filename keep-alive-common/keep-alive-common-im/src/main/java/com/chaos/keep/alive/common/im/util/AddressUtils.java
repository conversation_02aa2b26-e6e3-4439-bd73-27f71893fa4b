package com.chaos.keep.alive.common.im.util;


import com.chaos.keep.alive.common.core.exception.SystemException;
import com.chaos.keep.alive.common.im.domain.address.AddressInstance;

/**
 * <AUTHOR>
 */
public class AddressUtils {

    public static AddressInstance parseAddress(String address) {
        String[] split = address.split(":");
        if (split.length != 3) {
            throw new SystemException("地址解析错误");
        }
        String serverId = split[0];
        String ip = split[1];
        int port = Integer.parseInt(split[2]);
        return new AddressInstance(serverId, ip, port);
    }
}
