package com.chaos.keep.alive.common.im.domain;

import com.chaos.keep.alive.common.core.domain.BaseDomain;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class MessageSendJsonRequest extends BaseDomain {

    private Long messageId;

    private String fromOperatorNo;

    private String toOperatorNo;

    private Long chatId;

    private Integer chatType;

    private Long groupId;

    private String content;

    private Integer category;

    private String toAppId;

    private Long sequence;


}
