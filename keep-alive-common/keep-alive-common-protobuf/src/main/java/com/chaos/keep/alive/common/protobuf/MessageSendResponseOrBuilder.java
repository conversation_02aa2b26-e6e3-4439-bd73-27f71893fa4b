// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MessageSendResponse.proto

package com.chaos.keep.alive.common.protobuf;

public interface MessageSendResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.chaos.ka.MessageSendResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>uint64 messageId = 1;</code>
   * @return The messageId.
   */
  long getMessageId();

  /**
   * <code>uint32 chatType = 2;</code>
   * @return The chatType.
   */
  int getChatType();

  /**
   * <code>string fromOperatorNo = 3;</code>
   * @return The fromOperatorNo.
   */
  java.lang.String getFromOperatorNo();
  /**
   * <code>string fromOperatorNo = 3;</code>
   * @return The bytes for fromOperatorNo.
   */
  com.google.protobuf.ByteString
      getFromOperatorNoBytes();

  /**
   * <code>string toOperatorNo = 4;</code>
   * @return The toOperatorNo.
   */
  java.lang.String getToOperatorNo();
  /**
   * <code>string toOperatorNo = 4;</code>
   * @return The bytes for toOperatorNo.
   */
  com.google.protobuf.ByteString
      getToOperatorNoBytes();

  /**
   * <code>uint64 sequence = 5;</code>
   * @return The sequence.
   */
  long getSequence();

  /**
   * <code>uint64 timestamp = 6;</code>
   * @return The timestamp.
   */
  long getTimestamp();

  /**
   * <code>uint64 chatId = 7;</code>
   * @return The chatId.
   */
  long getChatId();
}
