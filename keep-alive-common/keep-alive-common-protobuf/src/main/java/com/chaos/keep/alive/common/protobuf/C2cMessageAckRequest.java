// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: C2cMessageAckRequest.proto

package com.chaos.keep.alive.common.protobuf;

/**
 * Protobuf type {@code com.chaos.ka.C2cMessageAckRequest}
 */
public final class C2cMessageAckRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.chaos.ka.C2cMessageAckRequest)
    C2cMessageAckRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use C2cMessageAckRequest.newBuilder() to construct.
  private C2cMessageAckRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private C2cMessageAckRequest() {
    toOperatorNo_ = "";
    operatorNo_ = "";
    deviceId_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new C2cMessageAckRequest();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private C2cMessageAckRequest(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            chatType_ = input.readUInt32();
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            toOperatorNo_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            operatorNo_ = s;
            break;
          }
          case 32: {

            chatId_ = input.readUInt64();
            break;
          }
          case 40: {

            messageId_ = input.readUInt64();
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            deviceId_ = s;
            break;
          }
          case 56: {

            sequence_ = input.readUInt64();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.chaos.keep.alive.common.protobuf.C2cMessageAckRequestProto.internal_static_com_chaos_ka_C2cMessageAckRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.chaos.keep.alive.common.protobuf.C2cMessageAckRequestProto.internal_static_com_chaos_ka_C2cMessageAckRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest.class, com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest.Builder.class);
  }

  public static final int CHATTYPE_FIELD_NUMBER = 1;
  private int chatType_;
  /**
   * <code>uint32 chatType = 1;</code>
   * @return The chatType.
   */
  @java.lang.Override
  public int getChatType() {
    return chatType_;
  }

  public static final int TOOPERATORNO_FIELD_NUMBER = 2;
  private volatile java.lang.Object toOperatorNo_;
  /**
   * <code>string toOperatorNo = 2;</code>
   * @return The toOperatorNo.
   */
  @java.lang.Override
  public java.lang.String getToOperatorNo() {
    java.lang.Object ref = toOperatorNo_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      toOperatorNo_ = s;
      return s;
    }
  }
  /**
   * <code>string toOperatorNo = 2;</code>
   * @return The bytes for toOperatorNo.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getToOperatorNoBytes() {
    java.lang.Object ref = toOperatorNo_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      toOperatorNo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int OPERATORNO_FIELD_NUMBER = 3;
  private volatile java.lang.Object operatorNo_;
  /**
   * <code>string operatorNo = 3;</code>
   * @return The operatorNo.
   */
  @java.lang.Override
  public java.lang.String getOperatorNo() {
    java.lang.Object ref = operatorNo_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      operatorNo_ = s;
      return s;
    }
  }
  /**
   * <code>string operatorNo = 3;</code>
   * @return The bytes for operatorNo.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getOperatorNoBytes() {
    java.lang.Object ref = operatorNo_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      operatorNo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CHATID_FIELD_NUMBER = 4;
  private long chatId_;
  /**
   * <code>uint64 chatId = 4;</code>
   * @return The chatId.
   */
  @java.lang.Override
  public long getChatId() {
    return chatId_;
  }

  public static final int MESSAGEID_FIELD_NUMBER = 5;
  private long messageId_;
  /**
   * <code>uint64 messageId = 5;</code>
   * @return The messageId.
   */
  @java.lang.Override
  public long getMessageId() {
    return messageId_;
  }

  public static final int DEVICEID_FIELD_NUMBER = 6;
  private volatile java.lang.Object deviceId_;
  /**
   * <code>string deviceId = 6;</code>
   * @return The deviceId.
   */
  @java.lang.Override
  public java.lang.String getDeviceId() {
    java.lang.Object ref = deviceId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      deviceId_ = s;
      return s;
    }
  }
  /**
   * <code>string deviceId = 6;</code>
   * @return The bytes for deviceId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDeviceIdBytes() {
    java.lang.Object ref = deviceId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      deviceId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SEQUENCE_FIELD_NUMBER = 7;
  private long sequence_;
  /**
   * <code>uint64 sequence = 7;</code>
   * @return The sequence.
   */
  @java.lang.Override
  public long getSequence() {
    return sequence_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (chatType_ != 0) {
      output.writeUInt32(1, chatType_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(toOperatorNo_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, toOperatorNo_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(operatorNo_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, operatorNo_);
    }
    if (chatId_ != 0L) {
      output.writeUInt64(4, chatId_);
    }
    if (messageId_ != 0L) {
      output.writeUInt64(5, messageId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(deviceId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, deviceId_);
    }
    if (sequence_ != 0L) {
      output.writeUInt64(7, sequence_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (chatType_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(1, chatType_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(toOperatorNo_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, toOperatorNo_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(operatorNo_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, operatorNo_);
    }
    if (chatId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(4, chatId_);
    }
    if (messageId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(5, messageId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(deviceId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, deviceId_);
    }
    if (sequence_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(7, sequence_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest)) {
      return super.equals(obj);
    }
    com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest other = (com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest) obj;

    if (getChatType()
        != other.getChatType()) return false;
    if (!getToOperatorNo()
        .equals(other.getToOperatorNo())) return false;
    if (!getOperatorNo()
        .equals(other.getOperatorNo())) return false;
    if (getChatId()
        != other.getChatId()) return false;
    if (getMessageId()
        != other.getMessageId()) return false;
    if (!getDeviceId()
        .equals(other.getDeviceId())) return false;
    if (getSequence()
        != other.getSequence()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + CHATTYPE_FIELD_NUMBER;
    hash = (53 * hash) + getChatType();
    hash = (37 * hash) + TOOPERATORNO_FIELD_NUMBER;
    hash = (53 * hash) + getToOperatorNo().hashCode();
    hash = (37 * hash) + OPERATORNO_FIELD_NUMBER;
    hash = (53 * hash) + getOperatorNo().hashCode();
    hash = (37 * hash) + CHATID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getChatId());
    hash = (37 * hash) + MESSAGEID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getMessageId());
    hash = (37 * hash) + DEVICEID_FIELD_NUMBER;
    hash = (53 * hash) + getDeviceId().hashCode();
    hash = (37 * hash) + SEQUENCE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getSequence());
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.chaos.ka.C2cMessageAckRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.chaos.ka.C2cMessageAckRequest)
      com.chaos.keep.alive.common.protobuf.C2cMessageAckRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.chaos.keep.alive.common.protobuf.C2cMessageAckRequestProto.internal_static_com_chaos_ka_C2cMessageAckRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.chaos.keep.alive.common.protobuf.C2cMessageAckRequestProto.internal_static_com_chaos_ka_C2cMessageAckRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest.class, com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest.Builder.class);
    }

    // Construct using com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      chatType_ = 0;

      toOperatorNo_ = "";

      operatorNo_ = "";

      chatId_ = 0L;

      messageId_ = 0L;

      deviceId_ = "";

      sequence_ = 0L;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.chaos.keep.alive.common.protobuf.C2cMessageAckRequestProto.internal_static_com_chaos_ka_C2cMessageAckRequest_descriptor;
    }

    @java.lang.Override
    public com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest getDefaultInstanceForType() {
      return com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest.getDefaultInstance();
    }

    @java.lang.Override
    public com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest build() {
      com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest buildPartial() {
      com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest result = new com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest(this);
      result.chatType_ = chatType_;
      result.toOperatorNo_ = toOperatorNo_;
      result.operatorNo_ = operatorNo_;
      result.chatId_ = chatId_;
      result.messageId_ = messageId_;
      result.deviceId_ = deviceId_;
      result.sequence_ = sequence_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest) {
        return mergeFrom((com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest other) {
      if (other == com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest.getDefaultInstance()) return this;
      if (other.getChatType() != 0) {
        setChatType(other.getChatType());
      }
      if (!other.getToOperatorNo().isEmpty()) {
        toOperatorNo_ = other.toOperatorNo_;
        onChanged();
      }
      if (!other.getOperatorNo().isEmpty()) {
        operatorNo_ = other.operatorNo_;
        onChanged();
      }
      if (other.getChatId() != 0L) {
        setChatId(other.getChatId());
      }
      if (other.getMessageId() != 0L) {
        setMessageId(other.getMessageId());
      }
      if (!other.getDeviceId().isEmpty()) {
        deviceId_ = other.deviceId_;
        onChanged();
      }
      if (other.getSequence() != 0L) {
        setSequence(other.getSequence());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int chatType_ ;
    /**
     * <code>uint32 chatType = 1;</code>
     * @return The chatType.
     */
    @java.lang.Override
    public int getChatType() {
      return chatType_;
    }
    /**
     * <code>uint32 chatType = 1;</code>
     * @param value The chatType to set.
     * @return This builder for chaining.
     */
    public Builder setChatType(int value) {
      
      chatType_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>uint32 chatType = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearChatType() {
      
      chatType_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object toOperatorNo_ = "";
    /**
     * <code>string toOperatorNo = 2;</code>
     * @return The toOperatorNo.
     */
    public java.lang.String getToOperatorNo() {
      java.lang.Object ref = toOperatorNo_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        toOperatorNo_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string toOperatorNo = 2;</code>
     * @return The bytes for toOperatorNo.
     */
    public com.google.protobuf.ByteString
        getToOperatorNoBytes() {
      java.lang.Object ref = toOperatorNo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        toOperatorNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string toOperatorNo = 2;</code>
     * @param value The toOperatorNo to set.
     * @return This builder for chaining.
     */
    public Builder setToOperatorNo(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      toOperatorNo_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string toOperatorNo = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearToOperatorNo() {
      
      toOperatorNo_ = getDefaultInstance().getToOperatorNo();
      onChanged();
      return this;
    }
    /**
     * <code>string toOperatorNo = 2;</code>
     * @param value The bytes for toOperatorNo to set.
     * @return This builder for chaining.
     */
    public Builder setToOperatorNoBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      toOperatorNo_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object operatorNo_ = "";
    /**
     * <code>string operatorNo = 3;</code>
     * @return The operatorNo.
     */
    public java.lang.String getOperatorNo() {
      java.lang.Object ref = operatorNo_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        operatorNo_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string operatorNo = 3;</code>
     * @return The bytes for operatorNo.
     */
    public com.google.protobuf.ByteString
        getOperatorNoBytes() {
      java.lang.Object ref = operatorNo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        operatorNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string operatorNo = 3;</code>
     * @param value The operatorNo to set.
     * @return This builder for chaining.
     */
    public Builder setOperatorNo(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      operatorNo_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string operatorNo = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearOperatorNo() {
      
      operatorNo_ = getDefaultInstance().getOperatorNo();
      onChanged();
      return this;
    }
    /**
     * <code>string operatorNo = 3;</code>
     * @param value The bytes for operatorNo to set.
     * @return This builder for chaining.
     */
    public Builder setOperatorNoBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      operatorNo_ = value;
      onChanged();
      return this;
    }

    private long chatId_ ;
    /**
     * <code>uint64 chatId = 4;</code>
     * @return The chatId.
     */
    @java.lang.Override
    public long getChatId() {
      return chatId_;
    }
    /**
     * <code>uint64 chatId = 4;</code>
     * @param value The chatId to set.
     * @return This builder for chaining.
     */
    public Builder setChatId(long value) {
      
      chatId_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>uint64 chatId = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearChatId() {
      
      chatId_ = 0L;
      onChanged();
      return this;
    }

    private long messageId_ ;
    /**
     * <code>uint64 messageId = 5;</code>
     * @return The messageId.
     */
    @java.lang.Override
    public long getMessageId() {
      return messageId_;
    }
    /**
     * <code>uint64 messageId = 5;</code>
     * @param value The messageId to set.
     * @return This builder for chaining.
     */
    public Builder setMessageId(long value) {
      
      messageId_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>uint64 messageId = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearMessageId() {
      
      messageId_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object deviceId_ = "";
    /**
     * <code>string deviceId = 6;</code>
     * @return The deviceId.
     */
    public java.lang.String getDeviceId() {
      java.lang.Object ref = deviceId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        deviceId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string deviceId = 6;</code>
     * @return The bytes for deviceId.
     */
    public com.google.protobuf.ByteString
        getDeviceIdBytes() {
      java.lang.Object ref = deviceId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        deviceId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string deviceId = 6;</code>
     * @param value The deviceId to set.
     * @return This builder for chaining.
     */
    public Builder setDeviceId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      deviceId_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string deviceId = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearDeviceId() {
      
      deviceId_ = getDefaultInstance().getDeviceId();
      onChanged();
      return this;
    }
    /**
     * <code>string deviceId = 6;</code>
     * @param value The bytes for deviceId to set.
     * @return This builder for chaining.
     */
    public Builder setDeviceIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      deviceId_ = value;
      onChanged();
      return this;
    }

    private long sequence_ ;
    /**
     * <code>uint64 sequence = 7;</code>
     * @return The sequence.
     */
    @java.lang.Override
    public long getSequence() {
      return sequence_;
    }
    /**
     * <code>uint64 sequence = 7;</code>
     * @param value The sequence to set.
     * @return This builder for chaining.
     */
    public Builder setSequence(long value) {
      
      sequence_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>uint64 sequence = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearSequence() {
      
      sequence_ = 0L;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.chaos.ka.C2cMessageAckRequest)
  }

  // @@protoc_insertion_point(class_scope:com.chaos.ka.C2cMessageAckRequest)
  private static final com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest();
  }

  public static com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<C2cMessageAckRequest>
      PARSER = new com.google.protobuf.AbstractParser<C2cMessageAckRequest>() {
    @java.lang.Override
    public C2cMessageAckRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new C2cMessageAckRequest(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<C2cMessageAckRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<C2cMessageAckRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

