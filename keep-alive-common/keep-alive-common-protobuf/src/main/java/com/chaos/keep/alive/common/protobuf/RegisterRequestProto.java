// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: RegisterRequest.proto

package com.chaos.keep.alive.common.protobuf;

public final class RegisterRequestProto {
  private RegisterRequestProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_chaos_ka_RegisterRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_chaos_ka_RegisterRequest_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\025RegisterRequest.proto\022\014com.chaos.ka\"#\n" +
      "\017RegisterRequest\022\020\n\010serverId\030\001 \001(\tB>\n$co" +
      "m.chaos.keep.alive.common.protobufB\024Regi" +
      "sterRequestProtoP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_chaos_ka_RegisterRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_chaos_ka_RegisterRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_chaos_ka_RegisterRequest_descriptor,
        new java.lang.String[] { "ServerId", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
