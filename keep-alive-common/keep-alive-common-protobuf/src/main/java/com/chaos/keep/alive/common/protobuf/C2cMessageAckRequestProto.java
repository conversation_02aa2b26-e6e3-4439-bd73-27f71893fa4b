// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: C2cMessageAckRequest.proto

package com.chaos.keep.alive.common.protobuf;

public final class C2cMessageAckRequestProto {
  private C2cMessageAckRequestProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_chaos_ka_C2cMessageAckRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_chaos_ka_C2cMessageAckRequest_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\032C2cMessageAckRequest.proto\022\014com.chaos." +
      "ka\"\231\001\n\024C2cMessageAckRequest\022\020\n\010chatType\030" +
      "\001 \001(\r\022\024\n\014toOperatorNo\030\002 \001(\t\022\022\n\noperatorN" +
      "o\030\003 \001(\t\022\016\n\006chatId\030\004 \001(\004\022\021\n\tmessageId\030\005 \001" +
      "(\004\022\020\n\010deviceId\030\006 \001(\t\022\020\n\010sequence\030\007 \001(\004BC" +
      "\n$com.chaos.keep.alive.common.protobufB\031" +
      "C2cMessageAckRequestProtoP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_chaos_ka_C2cMessageAckRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_chaos_ka_C2cMessageAckRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_chaos_ka_C2cMessageAckRequest_descriptor,
        new java.lang.String[] { "ChatType", "ToOperatorNo", "OperatorNo", "ChatId", "MessageId", "DeviceId", "Sequence", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
