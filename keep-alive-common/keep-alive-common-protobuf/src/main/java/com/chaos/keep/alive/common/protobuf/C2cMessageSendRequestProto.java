// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: C2cMessageSendRequest.proto

package com.chaos.keep.alive.common.protobuf;

public final class C2cMessageSendRequestProto {
  private C2cMessageSendRequestProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_chaos_ka_C2cMessageSendRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_chaos_ka_C2cMessageSendRequest_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\033C2cMessageSendRequest.proto\022\014com.chaos" +
      ".ka\"\235\001\n\025C2cMessageSendRequest\022\021\n\tmessage" +
      "Id\030\001 \001(\004\022\026\n\016fromOperatorNo\030\002 \001(\t\022\024\n\014toOp" +
      "eratorNo\030\003 \001(\t\022\016\n\006chatId\030\004 \001(\004\022\017\n\007conten" +
      "t\030\005 \001(\t\022\020\n\010category\030\006 \001(\r\022\020\n\010sequence\030\007 " +
      "\001(\004BD\n$com.chaos.keep.alive.common.proto" +
      "bufB\032C2cMessageSendRequestProtoP\001b\006proto" +
      "3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_chaos_ka_C2cMessageSendRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_chaos_ka_C2cMessageSendRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_chaos_ka_C2cMessageSendRequest_descriptor,
        new java.lang.String[] { "MessageId", "FromOperatorNo", "ToOperatorNo", "ChatId", "Content", "Category", "Sequence", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
