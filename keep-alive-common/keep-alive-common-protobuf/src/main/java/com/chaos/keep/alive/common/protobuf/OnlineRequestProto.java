// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: OnlineRequest.proto

package com.chaos.keep.alive.common.protobuf;

public final class OnlineRequestProto {
  private OnlineRequestProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_chaos_ka_OnlineRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_chaos_ka_OnlineRequest_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\023OnlineRequest.proto\022\014com.chaos.ka\"@\n\rO" +
      "nlineRequest\022\r\n\005token\030\001 \001(\t\022\021\n\tloginName" +
      "\030\002 \001(\t\022\r\n\005appNo\030\003 \001(\rB<\n$com.chaos.keep." +
      "alive.common.protobufB\022OnlineRequestProt" +
      "oP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_chaos_ka_OnlineRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_chaos_ka_OnlineRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_chaos_ka_OnlineRequest_descriptor,
        new java.lang.String[] { "Token", "LoginName", "AppNo", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
