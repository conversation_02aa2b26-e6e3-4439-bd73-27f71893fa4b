// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: RiderReportLocationRequest.proto

package com.chaos.keep.alive.common.protobuf;

public interface RiderReportLocationRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.chaos.ka.RiderReportLocationRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>double latitude = 1;</code>
   * @return The latitude.
   */
  double getLatitude();

  /**
   * <code>double longitude = 2;</code>
   * @return The longitude.
   */
  double getLongitude();

  /**
   * <code>uint64 riderId = 3;</code>
   * @return The riderId.
   */
  long getRiderId();
}
