// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Command.proto

package com.chaos.keep.alive.common.protobuf;

public final class CommandProto {
  private CommandProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_chaos_ka_Command_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_chaos_ka_Command_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\rCommand.proto\022\014com.chaos.ka\"\234\001\n\007Comman" +
      "d\022\025\n\rappSdkVersion\030\001 \001(\t\022\023\n\013messageType\030" +
      "\002 \001(\r\022\017\n\007bizType\030\003 \001(\r\022\021\n\ttimestamp\030\004 \001(" +
      "\004\022\022\n\noperatorNo\030\005 \001(\t\022\020\n\010deviceId\030\006 \001(\t\022" +
      "\r\n\005appId\030\007 \001(\t\022\014\n\004body\030\010 \001(\014B6\n$com.chao" +
      "s.keep.alive.common.protobufB\014CommandPro" +
      "toP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_chaos_ka_Command_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_chaos_ka_Command_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_chaos_ka_Command_descriptor,
        new java.lang.String[] { "AppSdkVersion", "MessageType", "BizType", "Timestamp", "OperatorNo", "DeviceId", "AppId", "Body", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
