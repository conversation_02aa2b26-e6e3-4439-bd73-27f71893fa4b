// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ImMessagePush.proto

package com.chaos.keep.alive.common.protobuf;

/**
 * Protobuf type {@code com.chaos.ka.ImMessagePush}
 */
public final class ImMessagePush extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.chaos.ka.ImMessagePush)
    ImMessagePushOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ImMessagePush.newBuilder() to construct.
  private ImMessagePush(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ImMessagePush() {
    content_ = "";
    fromOperatorNo_ = "";
    toOperatorNo_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ImMessagePush();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ImMessagePush(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            messageId_ = input.readUInt64();
            break;
          }
          case 16: {

            chatId_ = input.readUInt64();
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            content_ = s;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            fromOperatorNo_ = s;
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            toOperatorNo_ = s;
            break;
          }
          case 48: {

            category_ = input.readUInt32();
            break;
          }
          case 56: {

            chatType_ = input.readUInt32();
            break;
          }
          case 64: {

            sequence_ = input.readUInt64();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.chaos.keep.alive.common.protobuf.ImMessagePushProto.internal_static_com_chaos_ka_ImMessagePush_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.chaos.keep.alive.common.protobuf.ImMessagePushProto.internal_static_com_chaos_ka_ImMessagePush_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.chaos.keep.alive.common.protobuf.ImMessagePush.class, com.chaos.keep.alive.common.protobuf.ImMessagePush.Builder.class);
  }

  public static final int MESSAGEID_FIELD_NUMBER = 1;
  private long messageId_;
  /**
   * <code>uint64 messageId = 1;</code>
   * @return The messageId.
   */
  @java.lang.Override
  public long getMessageId() {
    return messageId_;
  }

  public static final int CHATID_FIELD_NUMBER = 2;
  private long chatId_;
  /**
   * <code>uint64 chatId = 2;</code>
   * @return The chatId.
   */
  @java.lang.Override
  public long getChatId() {
    return chatId_;
  }

  public static final int CONTENT_FIELD_NUMBER = 3;
  private volatile java.lang.Object content_;
  /**
   * <code>string content = 3;</code>
   * @return The content.
   */
  @java.lang.Override
  public java.lang.String getContent() {
    java.lang.Object ref = content_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      content_ = s;
      return s;
    }
  }
  /**
   * <code>string content = 3;</code>
   * @return The bytes for content.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getContentBytes() {
    java.lang.Object ref = content_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      content_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int FROMOPERATORNO_FIELD_NUMBER = 4;
  private volatile java.lang.Object fromOperatorNo_;
  /**
   * <code>string fromOperatorNo = 4;</code>
   * @return The fromOperatorNo.
   */
  @java.lang.Override
  public java.lang.String getFromOperatorNo() {
    java.lang.Object ref = fromOperatorNo_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      fromOperatorNo_ = s;
      return s;
    }
  }
  /**
   * <code>string fromOperatorNo = 4;</code>
   * @return The bytes for fromOperatorNo.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getFromOperatorNoBytes() {
    java.lang.Object ref = fromOperatorNo_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      fromOperatorNo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TOOPERATORNO_FIELD_NUMBER = 5;
  private volatile java.lang.Object toOperatorNo_;
  /**
   * <code>string toOperatorNo = 5;</code>
   * @return The toOperatorNo.
   */
  @java.lang.Override
  public java.lang.String getToOperatorNo() {
    java.lang.Object ref = toOperatorNo_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      toOperatorNo_ = s;
      return s;
    }
  }
  /**
   * <code>string toOperatorNo = 5;</code>
   * @return The bytes for toOperatorNo.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getToOperatorNoBytes() {
    java.lang.Object ref = toOperatorNo_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      toOperatorNo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CATEGORY_FIELD_NUMBER = 6;
  private int category_;
  /**
   * <code>uint32 category = 6;</code>
   * @return The category.
   */
  @java.lang.Override
  public int getCategory() {
    return category_;
  }

  public static final int CHATTYPE_FIELD_NUMBER = 7;
  private int chatType_;
  /**
   * <code>uint32 chatType = 7;</code>
   * @return The chatType.
   */
  @java.lang.Override
  public int getChatType() {
    return chatType_;
  }

  public static final int SEQUENCE_FIELD_NUMBER = 8;
  private long sequence_;
  /**
   * <code>uint64 sequence = 8;</code>
   * @return The sequence.
   */
  @java.lang.Override
  public long getSequence() {
    return sequence_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (messageId_ != 0L) {
      output.writeUInt64(1, messageId_);
    }
    if (chatId_ != 0L) {
      output.writeUInt64(2, chatId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(content_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, content_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(fromOperatorNo_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, fromOperatorNo_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(toOperatorNo_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, toOperatorNo_);
    }
    if (category_ != 0) {
      output.writeUInt32(6, category_);
    }
    if (chatType_ != 0) {
      output.writeUInt32(7, chatType_);
    }
    if (sequence_ != 0L) {
      output.writeUInt64(8, sequence_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (messageId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(1, messageId_);
    }
    if (chatId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(2, chatId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(content_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, content_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(fromOperatorNo_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, fromOperatorNo_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(toOperatorNo_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, toOperatorNo_);
    }
    if (category_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(6, category_);
    }
    if (chatType_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(7, chatType_);
    }
    if (sequence_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(8, sequence_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.chaos.keep.alive.common.protobuf.ImMessagePush)) {
      return super.equals(obj);
    }
    com.chaos.keep.alive.common.protobuf.ImMessagePush other = (com.chaos.keep.alive.common.protobuf.ImMessagePush) obj;

    if (getMessageId()
        != other.getMessageId()) return false;
    if (getChatId()
        != other.getChatId()) return false;
    if (!getContent()
        .equals(other.getContent())) return false;
    if (!getFromOperatorNo()
        .equals(other.getFromOperatorNo())) return false;
    if (!getToOperatorNo()
        .equals(other.getToOperatorNo())) return false;
    if (getCategory()
        != other.getCategory()) return false;
    if (getChatType()
        != other.getChatType()) return false;
    if (getSequence()
        != other.getSequence()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + MESSAGEID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getMessageId());
    hash = (37 * hash) + CHATID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getChatId());
    hash = (37 * hash) + CONTENT_FIELD_NUMBER;
    hash = (53 * hash) + getContent().hashCode();
    hash = (37 * hash) + FROMOPERATORNO_FIELD_NUMBER;
    hash = (53 * hash) + getFromOperatorNo().hashCode();
    hash = (37 * hash) + TOOPERATORNO_FIELD_NUMBER;
    hash = (53 * hash) + getToOperatorNo().hashCode();
    hash = (37 * hash) + CATEGORY_FIELD_NUMBER;
    hash = (53 * hash) + getCategory();
    hash = (37 * hash) + CHATTYPE_FIELD_NUMBER;
    hash = (53 * hash) + getChatType();
    hash = (37 * hash) + SEQUENCE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getSequence());
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.chaos.keep.alive.common.protobuf.ImMessagePush parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.chaos.keep.alive.common.protobuf.ImMessagePush parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.chaos.keep.alive.common.protobuf.ImMessagePush parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.chaos.keep.alive.common.protobuf.ImMessagePush parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.chaos.keep.alive.common.protobuf.ImMessagePush parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.chaos.keep.alive.common.protobuf.ImMessagePush parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.chaos.keep.alive.common.protobuf.ImMessagePush parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.chaos.keep.alive.common.protobuf.ImMessagePush parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.chaos.keep.alive.common.protobuf.ImMessagePush parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static com.chaos.keep.alive.common.protobuf.ImMessagePush parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.chaos.keep.alive.common.protobuf.ImMessagePush parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.chaos.keep.alive.common.protobuf.ImMessagePush parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.chaos.keep.alive.common.protobuf.ImMessagePush prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.chaos.ka.ImMessagePush}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.chaos.ka.ImMessagePush)
      com.chaos.keep.alive.common.protobuf.ImMessagePushOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.chaos.keep.alive.common.protobuf.ImMessagePushProto.internal_static_com_chaos_ka_ImMessagePush_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.chaos.keep.alive.common.protobuf.ImMessagePushProto.internal_static_com_chaos_ka_ImMessagePush_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.chaos.keep.alive.common.protobuf.ImMessagePush.class, com.chaos.keep.alive.common.protobuf.ImMessagePush.Builder.class);
    }

    // Construct using com.chaos.keep.alive.common.protobuf.ImMessagePush.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      messageId_ = 0L;

      chatId_ = 0L;

      content_ = "";

      fromOperatorNo_ = "";

      toOperatorNo_ = "";

      category_ = 0;

      chatType_ = 0;

      sequence_ = 0L;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.chaos.keep.alive.common.protobuf.ImMessagePushProto.internal_static_com_chaos_ka_ImMessagePush_descriptor;
    }

    @java.lang.Override
    public com.chaos.keep.alive.common.protobuf.ImMessagePush getDefaultInstanceForType() {
      return com.chaos.keep.alive.common.protobuf.ImMessagePush.getDefaultInstance();
    }

    @java.lang.Override
    public com.chaos.keep.alive.common.protobuf.ImMessagePush build() {
      com.chaos.keep.alive.common.protobuf.ImMessagePush result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.chaos.keep.alive.common.protobuf.ImMessagePush buildPartial() {
      com.chaos.keep.alive.common.protobuf.ImMessagePush result = new com.chaos.keep.alive.common.protobuf.ImMessagePush(this);
      result.messageId_ = messageId_;
      result.chatId_ = chatId_;
      result.content_ = content_;
      result.fromOperatorNo_ = fromOperatorNo_;
      result.toOperatorNo_ = toOperatorNo_;
      result.category_ = category_;
      result.chatType_ = chatType_;
      result.sequence_ = sequence_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.chaos.keep.alive.common.protobuf.ImMessagePush) {
        return mergeFrom((com.chaos.keep.alive.common.protobuf.ImMessagePush)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.chaos.keep.alive.common.protobuf.ImMessagePush other) {
      if (other == com.chaos.keep.alive.common.protobuf.ImMessagePush.getDefaultInstance()) return this;
      if (other.getMessageId() != 0L) {
        setMessageId(other.getMessageId());
      }
      if (other.getChatId() != 0L) {
        setChatId(other.getChatId());
      }
      if (!other.getContent().isEmpty()) {
        content_ = other.content_;
        onChanged();
      }
      if (!other.getFromOperatorNo().isEmpty()) {
        fromOperatorNo_ = other.fromOperatorNo_;
        onChanged();
      }
      if (!other.getToOperatorNo().isEmpty()) {
        toOperatorNo_ = other.toOperatorNo_;
        onChanged();
      }
      if (other.getCategory() != 0) {
        setCategory(other.getCategory());
      }
      if (other.getChatType() != 0) {
        setChatType(other.getChatType());
      }
      if (other.getSequence() != 0L) {
        setSequence(other.getSequence());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      com.chaos.keep.alive.common.protobuf.ImMessagePush parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (com.chaos.keep.alive.common.protobuf.ImMessagePush) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private long messageId_ ;
    /**
     * <code>uint64 messageId = 1;</code>
     * @return The messageId.
     */
    @java.lang.Override
    public long getMessageId() {
      return messageId_;
    }
    /**
     * <code>uint64 messageId = 1;</code>
     * @param value The messageId to set.
     * @return This builder for chaining.
     */
    public Builder setMessageId(long value) {
      
      messageId_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>uint64 messageId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearMessageId() {
      
      messageId_ = 0L;
      onChanged();
      return this;
    }

    private long chatId_ ;
    /**
     * <code>uint64 chatId = 2;</code>
     * @return The chatId.
     */
    @java.lang.Override
    public long getChatId() {
      return chatId_;
    }
    /**
     * <code>uint64 chatId = 2;</code>
     * @param value The chatId to set.
     * @return This builder for chaining.
     */
    public Builder setChatId(long value) {
      
      chatId_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>uint64 chatId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearChatId() {
      
      chatId_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object content_ = "";
    /**
     * <code>string content = 3;</code>
     * @return The content.
     */
    public java.lang.String getContent() {
      java.lang.Object ref = content_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        content_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string content = 3;</code>
     * @return The bytes for content.
     */
    public com.google.protobuf.ByteString
        getContentBytes() {
      java.lang.Object ref = content_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        content_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string content = 3;</code>
     * @param value The content to set.
     * @return This builder for chaining.
     */
    public Builder setContent(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      content_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string content = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearContent() {
      
      content_ = getDefaultInstance().getContent();
      onChanged();
      return this;
    }
    /**
     * <code>string content = 3;</code>
     * @param value The bytes for content to set.
     * @return This builder for chaining.
     */
    public Builder setContentBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      content_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object fromOperatorNo_ = "";
    /**
     * <code>string fromOperatorNo = 4;</code>
     * @return The fromOperatorNo.
     */
    public java.lang.String getFromOperatorNo() {
      java.lang.Object ref = fromOperatorNo_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        fromOperatorNo_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string fromOperatorNo = 4;</code>
     * @return The bytes for fromOperatorNo.
     */
    public com.google.protobuf.ByteString
        getFromOperatorNoBytes() {
      java.lang.Object ref = fromOperatorNo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fromOperatorNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string fromOperatorNo = 4;</code>
     * @param value The fromOperatorNo to set.
     * @return This builder for chaining.
     */
    public Builder setFromOperatorNo(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      fromOperatorNo_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string fromOperatorNo = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearFromOperatorNo() {
      
      fromOperatorNo_ = getDefaultInstance().getFromOperatorNo();
      onChanged();
      return this;
    }
    /**
     * <code>string fromOperatorNo = 4;</code>
     * @param value The bytes for fromOperatorNo to set.
     * @return This builder for chaining.
     */
    public Builder setFromOperatorNoBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      fromOperatorNo_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object toOperatorNo_ = "";
    /**
     * <code>string toOperatorNo = 5;</code>
     * @return The toOperatorNo.
     */
    public java.lang.String getToOperatorNo() {
      java.lang.Object ref = toOperatorNo_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        toOperatorNo_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string toOperatorNo = 5;</code>
     * @return The bytes for toOperatorNo.
     */
    public com.google.protobuf.ByteString
        getToOperatorNoBytes() {
      java.lang.Object ref = toOperatorNo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        toOperatorNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string toOperatorNo = 5;</code>
     * @param value The toOperatorNo to set.
     * @return This builder for chaining.
     */
    public Builder setToOperatorNo(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      toOperatorNo_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string toOperatorNo = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearToOperatorNo() {
      
      toOperatorNo_ = getDefaultInstance().getToOperatorNo();
      onChanged();
      return this;
    }
    /**
     * <code>string toOperatorNo = 5;</code>
     * @param value The bytes for toOperatorNo to set.
     * @return This builder for chaining.
     */
    public Builder setToOperatorNoBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      toOperatorNo_ = value;
      onChanged();
      return this;
    }

    private int category_ ;
    /**
     * <code>uint32 category = 6;</code>
     * @return The category.
     */
    @java.lang.Override
    public int getCategory() {
      return category_;
    }
    /**
     * <code>uint32 category = 6;</code>
     * @param value The category to set.
     * @return This builder for chaining.
     */
    public Builder setCategory(int value) {
      
      category_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>uint32 category = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearCategory() {
      
      category_ = 0;
      onChanged();
      return this;
    }

    private int chatType_ ;
    /**
     * <code>uint32 chatType = 7;</code>
     * @return The chatType.
     */
    @java.lang.Override
    public int getChatType() {
      return chatType_;
    }
    /**
     * <code>uint32 chatType = 7;</code>
     * @param value The chatType to set.
     * @return This builder for chaining.
     */
    public Builder setChatType(int value) {
      
      chatType_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>uint32 chatType = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearChatType() {
      
      chatType_ = 0;
      onChanged();
      return this;
    }

    private long sequence_ ;
    /**
     * <code>uint64 sequence = 8;</code>
     * @return The sequence.
     */
    @java.lang.Override
    public long getSequence() {
      return sequence_;
    }
    /**
     * <code>uint64 sequence = 8;</code>
     * @param value The sequence to set.
     * @return This builder for chaining.
     */
    public Builder setSequence(long value) {
      
      sequence_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>uint64 sequence = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearSequence() {
      
      sequence_ = 0L;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.chaos.ka.ImMessagePush)
  }

  // @@protoc_insertion_point(class_scope:com.chaos.ka.ImMessagePush)
  private static final com.chaos.keep.alive.common.protobuf.ImMessagePush DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.chaos.keep.alive.common.protobuf.ImMessagePush();
  }

  public static com.chaos.keep.alive.common.protobuf.ImMessagePush getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ImMessagePush>
      PARSER = new com.google.protobuf.AbstractParser<ImMessagePush>() {
    @java.lang.Override
    public ImMessagePush parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ImMessagePush(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ImMessagePush> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ImMessagePush> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.chaos.keep.alive.common.protobuf.ImMessagePush getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

