// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ReportLocationRequest.proto

package com.chaos.keep.alive.common.protobuf;

public interface ReportLocationRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.chaos.ka.ReportLocationRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>double latitude = 1;</code>
   * @return The latitude.
   */
  double getLatitude();

  /**
   * <code>double longitude = 2;</code>
   * @return The longitude.
   */
  double getLongitude();

  /**
   * <code>double speed = 3;</code>
   * @return The speed.
   */
  double getSpeed();

  /**
   * <code>uint32 direction = 4;</code>
   * @return The direction.
   */
  int getDirection();

  /**
   * <code>string orderId = 5;</code>
   * @return The orderId.
   */
  java.lang.String getOrderId();
  /**
   * <code>string orderId = 5;</code>
   * @return The bytes for orderId.
   */
  com.google.protobuf.ByteString
      getOrderIdBytes();

  /**
   * <code>uint64 driverId = 6;</code>
   * @return The driverId.
   */
  long getDriverId();
}
