// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MessageSendResponse.proto

package com.chaos.keep.alive.common.protobuf;

public final class MessageSendResponseProto {
  private MessageSendResponseProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_chaos_ka_MessageSendResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_chaos_ka_MessageSendResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\031MessageSendResponse.proto\022\014com.chaos.k" +
      "a\"\235\001\n\023MessageSendResponse\022\021\n\tmessageId\030\001" +
      " \001(\004\022\020\n\010chatType\030\002 \001(\r\022\026\n\016fromOperatorNo" +
      "\030\003 \001(\t\022\024\n\014toOperatorNo\030\004 \001(\t\022\020\n\010sequence" +
      "\030\005 \001(\004\022\021\n\ttimestamp\030\006 \001(\004\022\016\n\006chatId\030\007 \001(" +
      "\004BB\n$com.chaos.keep.alive.common.protobu" +
      "fB\030MessageSendResponseProtoP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_chaos_ka_MessageSendResponse_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_chaos_ka_MessageSendResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_chaos_ka_MessageSendResponse_descriptor,
        new java.lang.String[] { "MessageId", "ChatType", "FromOperatorNo", "ToOperatorNo", "Sequence", "Timestamp", "ChatId", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
