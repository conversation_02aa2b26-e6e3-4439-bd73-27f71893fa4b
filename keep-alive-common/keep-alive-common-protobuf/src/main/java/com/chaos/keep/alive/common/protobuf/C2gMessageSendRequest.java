// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: C2gMessageSendRequest.proto

package com.chaos.keep.alive.common.protobuf;

/**
 * Protobuf type {@code com.chaos.ka.C2gMessageSendRequest}
 */
public final class C2gMessageSendRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.chaos.ka.C2gMessageSendRequest)
    C2gMessageSendRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use C2gMessageSendRequest.newBuilder() to construct.
  private C2gMessageSendRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private C2gMessageSendRequest() {
    fromOperatorNo_ = "";
    content_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new C2gMessageSendRequest();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private C2gMessageSendRequest(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            messageId_ = input.readUInt64();
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            fromOperatorNo_ = s;
            break;
          }
          case 24: {

            chatId_ = input.readUInt64();
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            content_ = s;
            break;
          }
          case 40: {

            category_ = input.readUInt32();
            break;
          }
          case 48: {

            sequence_ = input.readUInt64();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.chaos.keep.alive.common.protobuf.C2gMessageSendRequestProto.internal_static_com_chaos_ka_C2gMessageSendRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.chaos.keep.alive.common.protobuf.C2gMessageSendRequestProto.internal_static_com_chaos_ka_C2gMessageSendRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest.class, com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest.Builder.class);
  }

  public static final int MESSAGEID_FIELD_NUMBER = 1;
  private long messageId_;
  /**
   * <code>uint64 messageId = 1;</code>
   * @return The messageId.
   */
  @java.lang.Override
  public long getMessageId() {
    return messageId_;
  }

  public static final int FROMOPERATORNO_FIELD_NUMBER = 2;
  private volatile java.lang.Object fromOperatorNo_;
  /**
   * <code>string fromOperatorNo = 2;</code>
   * @return The fromOperatorNo.
   */
  @java.lang.Override
  public java.lang.String getFromOperatorNo() {
    java.lang.Object ref = fromOperatorNo_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      fromOperatorNo_ = s;
      return s;
    }
  }
  /**
   * <code>string fromOperatorNo = 2;</code>
   * @return The bytes for fromOperatorNo.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getFromOperatorNoBytes() {
    java.lang.Object ref = fromOperatorNo_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      fromOperatorNo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CHATID_FIELD_NUMBER = 3;
  private long chatId_;
  /**
   * <code>uint64 chatId = 3;</code>
   * @return The chatId.
   */
  @java.lang.Override
  public long getChatId() {
    return chatId_;
  }

  public static final int CONTENT_FIELD_NUMBER = 4;
  private volatile java.lang.Object content_;
  /**
   * <code>string content = 4;</code>
   * @return The content.
   */
  @java.lang.Override
  public java.lang.String getContent() {
    java.lang.Object ref = content_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      content_ = s;
      return s;
    }
  }
  /**
   * <code>string content = 4;</code>
   * @return The bytes for content.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getContentBytes() {
    java.lang.Object ref = content_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      content_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CATEGORY_FIELD_NUMBER = 5;
  private int category_;
  /**
   * <code>uint32 category = 5;</code>
   * @return The category.
   */
  @java.lang.Override
  public int getCategory() {
    return category_;
  }

  public static final int SEQUENCE_FIELD_NUMBER = 6;
  private long sequence_;
  /**
   * <code>uint64 sequence = 6;</code>
   * @return The sequence.
   */
  @java.lang.Override
  public long getSequence() {
    return sequence_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (messageId_ != 0L) {
      output.writeUInt64(1, messageId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(fromOperatorNo_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, fromOperatorNo_);
    }
    if (chatId_ != 0L) {
      output.writeUInt64(3, chatId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(content_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, content_);
    }
    if (category_ != 0) {
      output.writeUInt32(5, category_);
    }
    if (sequence_ != 0L) {
      output.writeUInt64(6, sequence_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (messageId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(1, messageId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(fromOperatorNo_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, fromOperatorNo_);
    }
    if (chatId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(3, chatId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(content_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, content_);
    }
    if (category_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(5, category_);
    }
    if (sequence_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(6, sequence_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest)) {
      return super.equals(obj);
    }
    com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest other = (com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest) obj;

    if (getMessageId()
        != other.getMessageId()) return false;
    if (!getFromOperatorNo()
        .equals(other.getFromOperatorNo())) return false;
    if (getChatId()
        != other.getChatId()) return false;
    if (!getContent()
        .equals(other.getContent())) return false;
    if (getCategory()
        != other.getCategory()) return false;
    if (getSequence()
        != other.getSequence()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + MESSAGEID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getMessageId());
    hash = (37 * hash) + FROMOPERATORNO_FIELD_NUMBER;
    hash = (53 * hash) + getFromOperatorNo().hashCode();
    hash = (37 * hash) + CHATID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getChatId());
    hash = (37 * hash) + CONTENT_FIELD_NUMBER;
    hash = (53 * hash) + getContent().hashCode();
    hash = (37 * hash) + CATEGORY_FIELD_NUMBER;
    hash = (53 * hash) + getCategory();
    hash = (37 * hash) + SEQUENCE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getSequence());
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.chaos.ka.C2gMessageSendRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.chaos.ka.C2gMessageSendRequest)
      com.chaos.keep.alive.common.protobuf.C2gMessageSendRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.chaos.keep.alive.common.protobuf.C2gMessageSendRequestProto.internal_static_com_chaos_ka_C2gMessageSendRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.chaos.keep.alive.common.protobuf.C2gMessageSendRequestProto.internal_static_com_chaos_ka_C2gMessageSendRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest.class, com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest.Builder.class);
    }

    // Construct using com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      messageId_ = 0L;

      fromOperatorNo_ = "";

      chatId_ = 0L;

      content_ = "";

      category_ = 0;

      sequence_ = 0L;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.chaos.keep.alive.common.protobuf.C2gMessageSendRequestProto.internal_static_com_chaos_ka_C2gMessageSendRequest_descriptor;
    }

    @java.lang.Override
    public com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest getDefaultInstanceForType() {
      return com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest.getDefaultInstance();
    }

    @java.lang.Override
    public com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest build() {
      com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest buildPartial() {
      com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest result = new com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest(this);
      result.messageId_ = messageId_;
      result.fromOperatorNo_ = fromOperatorNo_;
      result.chatId_ = chatId_;
      result.content_ = content_;
      result.category_ = category_;
      result.sequence_ = sequence_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest) {
        return mergeFrom((com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest other) {
      if (other == com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest.getDefaultInstance()) return this;
      if (other.getMessageId() != 0L) {
        setMessageId(other.getMessageId());
      }
      if (!other.getFromOperatorNo().isEmpty()) {
        fromOperatorNo_ = other.fromOperatorNo_;
        onChanged();
      }
      if (other.getChatId() != 0L) {
        setChatId(other.getChatId());
      }
      if (!other.getContent().isEmpty()) {
        content_ = other.content_;
        onChanged();
      }
      if (other.getCategory() != 0) {
        setCategory(other.getCategory());
      }
      if (other.getSequence() != 0L) {
        setSequence(other.getSequence());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private long messageId_ ;
    /**
     * <code>uint64 messageId = 1;</code>
     * @return The messageId.
     */
    @java.lang.Override
    public long getMessageId() {
      return messageId_;
    }
    /**
     * <code>uint64 messageId = 1;</code>
     * @param value The messageId to set.
     * @return This builder for chaining.
     */
    public Builder setMessageId(long value) {
      
      messageId_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>uint64 messageId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearMessageId() {
      
      messageId_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object fromOperatorNo_ = "";
    /**
     * <code>string fromOperatorNo = 2;</code>
     * @return The fromOperatorNo.
     */
    public java.lang.String getFromOperatorNo() {
      java.lang.Object ref = fromOperatorNo_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        fromOperatorNo_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string fromOperatorNo = 2;</code>
     * @return The bytes for fromOperatorNo.
     */
    public com.google.protobuf.ByteString
        getFromOperatorNoBytes() {
      java.lang.Object ref = fromOperatorNo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fromOperatorNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string fromOperatorNo = 2;</code>
     * @param value The fromOperatorNo to set.
     * @return This builder for chaining.
     */
    public Builder setFromOperatorNo(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      fromOperatorNo_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string fromOperatorNo = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearFromOperatorNo() {
      
      fromOperatorNo_ = getDefaultInstance().getFromOperatorNo();
      onChanged();
      return this;
    }
    /**
     * <code>string fromOperatorNo = 2;</code>
     * @param value The bytes for fromOperatorNo to set.
     * @return This builder for chaining.
     */
    public Builder setFromOperatorNoBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      fromOperatorNo_ = value;
      onChanged();
      return this;
    }

    private long chatId_ ;
    /**
     * <code>uint64 chatId = 3;</code>
     * @return The chatId.
     */
    @java.lang.Override
    public long getChatId() {
      return chatId_;
    }
    /**
     * <code>uint64 chatId = 3;</code>
     * @param value The chatId to set.
     * @return This builder for chaining.
     */
    public Builder setChatId(long value) {
      
      chatId_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>uint64 chatId = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearChatId() {
      
      chatId_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object content_ = "";
    /**
     * <code>string content = 4;</code>
     * @return The content.
     */
    public java.lang.String getContent() {
      java.lang.Object ref = content_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        content_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string content = 4;</code>
     * @return The bytes for content.
     */
    public com.google.protobuf.ByteString
        getContentBytes() {
      java.lang.Object ref = content_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        content_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string content = 4;</code>
     * @param value The content to set.
     * @return This builder for chaining.
     */
    public Builder setContent(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      content_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string content = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearContent() {
      
      content_ = getDefaultInstance().getContent();
      onChanged();
      return this;
    }
    /**
     * <code>string content = 4;</code>
     * @param value The bytes for content to set.
     * @return This builder for chaining.
     */
    public Builder setContentBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      content_ = value;
      onChanged();
      return this;
    }

    private int category_ ;
    /**
     * <code>uint32 category = 5;</code>
     * @return The category.
     */
    @java.lang.Override
    public int getCategory() {
      return category_;
    }
    /**
     * <code>uint32 category = 5;</code>
     * @param value The category to set.
     * @return This builder for chaining.
     */
    public Builder setCategory(int value) {
      
      category_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>uint32 category = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearCategory() {
      
      category_ = 0;
      onChanged();
      return this;
    }

    private long sequence_ ;
    /**
     * <code>uint64 sequence = 6;</code>
     * @return The sequence.
     */
    @java.lang.Override
    public long getSequence() {
      return sequence_;
    }
    /**
     * <code>uint64 sequence = 6;</code>
     * @param value The sequence to set.
     * @return This builder for chaining.
     */
    public Builder setSequence(long value) {
      
      sequence_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>uint64 sequence = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearSequence() {
      
      sequence_ = 0L;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.chaos.ka.C2gMessageSendRequest)
  }

  // @@protoc_insertion_point(class_scope:com.chaos.ka.C2gMessageSendRequest)
  private static final com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest();
  }

  public static com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<C2gMessageSendRequest>
      PARSER = new com.google.protobuf.AbstractParser<C2gMessageSendRequest>() {
    @java.lang.Override
    public C2gMessageSendRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new C2gMessageSendRequest(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<C2gMessageSendRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<C2gMessageSendRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.chaos.keep.alive.common.protobuf.C2gMessageSendRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

