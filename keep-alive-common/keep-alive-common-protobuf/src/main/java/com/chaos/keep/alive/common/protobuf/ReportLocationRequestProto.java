// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ReportLocationRequest.proto

package com.chaos.keep.alive.common.protobuf;

public final class ReportLocationRequestProto {
  private ReportLocationRequestProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_chaos_ka_ReportLocationRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_chaos_ka_ReportLocationRequest_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\033ReportLocationRequest.proto\022\014com.chaos" +
      ".ka\"\201\001\n\025ReportLocationRequest\022\020\n\010latitud" +
      "e\030\001 \001(\001\022\021\n\tlongitude\030\002 \001(\001\022\r\n\005speed\030\003 \001(" +
      "\001\022\021\n\tdirection\030\004 \001(\r\022\017\n\007orderId\030\005 \001(\t\022\020\n" +
      "\010driverId\030\006 \001(\004BD\n$com.chaos.keep.alive." +
      "common.protobufB\032ReportLocationRequestPr" +
      "otoP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_chaos_ka_ReportLocationRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_chaos_ka_ReportLocationRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_chaos_ka_ReportLocationRequest_descriptor,
        new java.lang.String[] { "Latitude", "Longitude", "Speed", "Direction", "OrderId", "DriverId", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
