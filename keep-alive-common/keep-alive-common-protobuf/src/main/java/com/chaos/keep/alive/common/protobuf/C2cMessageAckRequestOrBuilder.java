// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: C2cMessageAckRequest.proto

package com.chaos.keep.alive.common.protobuf;

public interface C2cMessageAckRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.chaos.ka.C2cMessageAckRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>uint32 chatType = 1;</code>
   * @return The chatType.
   */
  int getChatType();

  /**
   * <code>string toOperatorNo = 2;</code>
   * @return The toOperatorNo.
   */
  java.lang.String getToOperatorNo();
  /**
   * <code>string toOperatorNo = 2;</code>
   * @return The bytes for toOperatorNo.
   */
  com.google.protobuf.ByteString
      getToOperatorNoBytes();

  /**
   * <code>string operatorNo = 3;</code>
   * @return The operatorNo.
   */
  java.lang.String getOperatorNo();
  /**
   * <code>string operatorNo = 3;</code>
   * @return The bytes for operatorNo.
   */
  com.google.protobuf.ByteString
      getOperatorNoBytes();

  /**
   * <code>uint64 chatId = 4;</code>
   * @return The chatId.
   */
  long getChatId();

  /**
   * <code>uint64 messageId = 5;</code>
   * @return The messageId.
   */
  long getMessageId();

  /**
   * <code>string deviceId = 6;</code>
   * @return The deviceId.
   */
  java.lang.String getDeviceId();
  /**
   * <code>string deviceId = 6;</code>
   * @return The bytes for deviceId.
   */
  com.google.protobuf.ByteString
      getDeviceIdBytes();

  /**
   * <code>uint64 sequence = 7;</code>
   * @return The sequence.
   */
  long getSequence();
}
