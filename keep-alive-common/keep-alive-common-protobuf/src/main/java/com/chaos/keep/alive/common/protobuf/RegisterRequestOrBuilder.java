// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: RegisterRequest.proto

package com.chaos.keep.alive.common.protobuf;

public interface RegisterRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.chaos.ka.RegisterRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string serverId = 1;</code>
   * @return The serverId.
   */
  java.lang.String getServerId();
  /**
   * <code>string serverId = 1;</code>
   * @return The bytes for serverId.
   */
  com.google.protobuf.ByteString
      getServerIdBytes();
}
