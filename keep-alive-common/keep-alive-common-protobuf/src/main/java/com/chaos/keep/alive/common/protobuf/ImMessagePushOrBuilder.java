// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ImMessagePush.proto

package com.chaos.keep.alive.common.protobuf;

public interface ImMessagePushOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.chaos.ka.ImMessagePush)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>uint64 messageId = 1;</code>
   * @return The messageId.
   */
  long getMessageId();

  /**
   * <code>uint64 chatId = 2;</code>
   * @return The chatId.
   */
  long getChatId();

  /**
   * <code>string content = 3;</code>
   * @return The content.
   */
  java.lang.String getContent();
  /**
   * <code>string content = 3;</code>
   * @return The bytes for content.
   */
  com.google.protobuf.ByteString
      getContentBytes();

  /**
   * <code>string fromOperatorNo = 4;</code>
   * @return The fromOperatorNo.
   */
  java.lang.String getFromOperatorNo();
  /**
   * <code>string fromOperatorNo = 4;</code>
   * @return The bytes for fromOperatorNo.
   */
  com.google.protobuf.ByteString
      getFromOperatorNoBytes();

  /**
   * <code>string toOperatorNo = 5;</code>
   * @return The toOperatorNo.
   */
  java.lang.String getToOperatorNo();
  /**
   * <code>string toOperatorNo = 5;</code>
   * @return The bytes for toOperatorNo.
   */
  com.google.protobuf.ByteString
      getToOperatorNoBytes();

  /**
   * <code>uint32 category = 6;</code>
   * @return The category.
   */
  int getCategory();

  /**
   * <code>uint32 chatType = 7;</code>
   * @return The chatType.
   */
  int getChatType();

  /**
   * <code>uint64 sequence = 8;</code>
   * @return The sequence.
   */
  long getSequence();
}
