// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: C2gMessageSendRequest.proto

package com.chaos.keep.alive.common.protobuf;

public interface C2gMessageSendRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.chaos.ka.C2gMessageSendRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>uint64 messageId = 1;</code>
   * @return The messageId.
   */
  long getMessageId();

  /**
   * <code>string fromOperatorNo = 2;</code>
   * @return The fromOperatorNo.
   */
  java.lang.String getFromOperatorNo();
  /**
   * <code>string fromOperatorNo = 2;</code>
   * @return The bytes for fromOperatorNo.
   */
  com.google.protobuf.ByteString
      getFromOperatorNoBytes();

  /**
   * <code>uint64 chatId = 3;</code>
   * @return The chatId.
   */
  long getChatId();

  /**
   * <code>string content = 4;</code>
   * @return The content.
   */
  java.lang.String getContent();
  /**
   * <code>string content = 4;</code>
   * @return The bytes for content.
   */
  com.google.protobuf.ByteString
      getContentBytes();

  /**
   * <code>uint32 category = 5;</code>
   * @return The category.
   */
  int getCategory();

  /**
   * <code>uint64 sequence = 6;</code>
   * @return The sequence.
   */
  long getSequence();
}
