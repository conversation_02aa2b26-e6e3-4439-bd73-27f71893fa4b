// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ImMessagePush.proto

package com.chaos.keep.alive.common.protobuf;

public final class ImMessagePushProto {
  private ImMessagePushProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_chaos_ka_ImMessagePush_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_chaos_ka_ImMessagePush_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\023ImMessagePush.proto\022\014com.chaos.ka\"\247\001\n\r" +
      "ImMessagePush\022\021\n\tmessageId\030\001 \001(\004\022\016\n\006chat" +
      "Id\030\002 \001(\004\022\017\n\007content\030\003 \001(\t\022\026\n\016fromOperato" +
      "rNo\030\004 \001(\t\022\024\n\014toOperatorNo\030\005 \001(\t\022\020\n\010categ" +
      "ory\030\006 \001(\r\022\020\n\010chatType\030\007 \001(\r\022\020\n\010sequence\030" +
      "\010 \001(\004B<\n$com.chaos.keep.alive.common.pro" +
      "tobufB\022ImMessagePushProtoP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_chaos_ka_ImMessagePush_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_chaos_ka_ImMessagePush_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_chaos_ka_ImMessagePush_descriptor,
        new java.lang.String[] { "MessageId", "ChatId", "Content", "FromOperatorNo", "ToOperatorNo", "Category", "ChatType", "Sequence", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
