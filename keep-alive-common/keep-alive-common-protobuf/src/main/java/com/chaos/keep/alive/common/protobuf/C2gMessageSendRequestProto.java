// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: C2gMessageSendRequest.proto

package com.chaos.keep.alive.common.protobuf;

public final class C2gMessageSendRequestProto {
  private C2gMessageSendRequestProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_chaos_ka_C2gMessageSendRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_chaos_ka_C2gMessageSendRequest_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\033C2gMessageSendRequest.proto\022\014com.chaos" +
      ".ka\"\207\001\n\025C2gMessageSendRequest\022\021\n\tmessage" +
      "Id\030\001 \001(\004\022\026\n\016fromOperatorNo\030\002 \001(\t\022\016\n\006chat" +
      "Id\030\003 \001(\004\022\017\n\007content\030\004 \001(\t\022\020\n\010category\030\005 " +
      "\001(\r\022\020\n\010sequence\030\006 \001(\004BD\n$com.chaos.keep." +
      "alive.common.protobufB\032C2gMessageSendReq" +
      "uestProtoP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_chaos_ka_C2gMessageSendRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_chaos_ka_C2gMessageSendRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_chaos_ka_C2gMessageSendRequest_descriptor,
        new java.lang.String[] { "MessageId", "FromOperatorNo", "ChatId", "Content", "Category", "Sequence", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
