// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: RiderReportLocationRequest.proto

package com.chaos.keep.alive.common.protobuf;

public final class RiderReportLocationRequestProto {
  private RiderReportLocationRequestProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_chaos_ka_RiderReportLocationRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_chaos_ka_RiderReportLocationRequest_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n RiderReportLocationRequest.proto\022\014com." +
      "chaos.ka\"R\n\032RiderReportLocationRequest\022\020" +
      "\n\010latitude\030\001 \001(\001\022\021\n\tlongitude\030\002 \001(\001\022\017\n\007r" +
      "iderId\030\003 \001(\004BI\n$com.chaos.keep.alive.com" +
      "mon.protobufB\037RiderReportLocationRequest" +
      "ProtoP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_chaos_ka_RiderReportLocationRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_chaos_ka_RiderReportLocationRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_chaos_ka_RiderReportLocationRequest_descriptor,
        new java.lang.String[] { "Latitude", "Longitude", "RiderId", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
