// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: OnlineRequest.proto

package com.chaos.keep.alive.common.protobuf;

public interface OnlineRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.chaos.ka.OnlineRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string token = 1;</code>
   * @return The token.
   */
  java.lang.String getToken();
  /**
   * <code>string token = 1;</code>
   * @return The bytes for token.
   */
  com.google.protobuf.ByteString
      getTokenBytes();

  /**
   * <code>string loginName = 2;</code>
   * @return The loginName.
   */
  java.lang.String getLoginName();
  /**
   * <code>string loginName = 2;</code>
   * @return The bytes for loginName.
   */
  com.google.protobuf.ByteString
      getLoginNameBytes();

  /**
   * <code>uint32 appNo = 3;</code>
   * @return The appNo.
   */
  int getAppNo();
}
