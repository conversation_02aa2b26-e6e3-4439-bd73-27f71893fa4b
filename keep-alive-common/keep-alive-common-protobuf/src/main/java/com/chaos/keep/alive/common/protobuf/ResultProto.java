// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Result.proto

package com.chaos.keep.alive.common.protobuf;

public final class ResultProto {
  private ResultProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_chaos_ka_Result_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_chaos_ka_Result_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\014Result.proto\022\014com.chaos.ka\"P\n\006Result\022\017" +
      "\n\007success\030\001 \001(\010\022\021\n\terrorCode\030\002 \001(\t\022\024\n\014er" +
      "rorMessage\030\003 \001(\t\022\014\n\004data\030\004 \001(\014B5\n$com.ch" +
      "aos.keep.alive.common.protobufB\013ResultPr" +
      "otoP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_chaos_ka_Result_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_chaos_ka_Result_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_chaos_ka_Result_descriptor,
        new java.lang.String[] { "Success", "ErrorCode", "ErrorMessage", "Data", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
