// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: C2gMessageAckRequest.proto

package com.chaos.keep.alive.common.protobuf;

public final class C2gMessageAckRequestProto {
  private C2gMessageAckRequestProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_chaos_ka_C2gMessageAckRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_chaos_ka_C2gMessageAckRequest_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\032C2gMessageAckRequest.proto\022\014com.chaos." +
      "ka\"\203\001\n\024C2gMessageAckRequest\022\020\n\010chatType\030" +
      "\001 \001(\r\022\016\n\006chatId\030\002 \001(\004\022\022\n\noperatorNo\030\003 \001(" +
      "\t\022\021\n\tmessageId\030\004 \001(\004\022\020\n\010deviceId\030\005 \001(\t\022\020" +
      "\n\010sequence\030\006 \001(\004BC\n$com.chaos.keep.alive" +
      ".common.protobufB\031C2gMessageAckRequestPr" +
      "otoP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_chaos_ka_C2gMessageAckRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_chaos_ka_C2gMessageAckRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_chaos_ka_C2gMessageAckRequest_descriptor,
        new java.lang.String[] { "ChatType", "ChatId", "OperatorNo", "MessageId", "DeviceId", "Sequence", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
