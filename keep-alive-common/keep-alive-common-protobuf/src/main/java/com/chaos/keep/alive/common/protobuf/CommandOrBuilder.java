// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Command.proto

package com.chaos.keep.alive.common.protobuf;

public interface CommandOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.chaos.ka.Command)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string appSdkVersion = 1;</code>
   * @return The appSdkVersion.
   */
  java.lang.String getAppSdkVersion();
  /**
   * <code>string appSdkVersion = 1;</code>
   * @return The bytes for appSdkVersion.
   */
  com.google.protobuf.ByteString
      getAppSdkVersionBytes();

  /**
   * <code>uint32 messageType = 2;</code>
   * @return The messageType.
   */
  int getMessageType();

  /**
   * <code>uint32 bizType = 3;</code>
   * @return The bizType.
   */
  int getBizType();

  /**
   * <code>uint64 timestamp = 4;</code>
   * @return The timestamp.
   */
  long getTimestamp();

  /**
   * <code>string operatorNo = 5;</code>
   * @return The operatorNo.
   */
  java.lang.String getOperatorNo();
  /**
   * <code>string operatorNo = 5;</code>
   * @return The bytes for operatorNo.
   */
  com.google.protobuf.ByteString
      getOperatorNoBytes();

  /**
   * <code>string deviceId = 6;</code>
   * @return The deviceId.
   */
  java.lang.String getDeviceId();
  /**
   * <code>string deviceId = 6;</code>
   * @return The bytes for deviceId.
   */
  com.google.protobuf.ByteString
      getDeviceIdBytes();

  /**
   * <code>string appId = 7;</code>
   * @return The appId.
   */
  java.lang.String getAppId();
  /**
   * <code>string appId = 7;</code>
   * @return The bytes for appId.
   */
  com.google.protobuf.ByteString
      getAppIdBytes();

  /**
   * <code>bytes body = 8;</code>
   * @return The body.
   */
  com.google.protobuf.ByteString getBody();
}
