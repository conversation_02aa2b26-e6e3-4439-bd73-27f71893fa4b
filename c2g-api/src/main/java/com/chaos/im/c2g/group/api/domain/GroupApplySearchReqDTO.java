package com.chaos.im.c2g.group.api.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
public class GroupApplySearchReqDTO extends PageReqInfoDTO  {
    private static final long serialVersionUID = 1100062528415595444L;

    /**
     * 申请单编号
     */
    private String applyNo;

    /**
     * 申请场景
     */
    private String applyScene;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 群id
     */
    private String groupId;

    /**
     * 状态，枚举：ImGroupApplyStatusEnum
     */
    private Integer status;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 申请人操作员类型
     */
    private Integer operatorType;

    /**
     * 语言
     */
    private String language;

    /**
     * 受理人名称
     */
    private String acceptOperatorName;

    /**
     * 处理人名称
     */
    private String handleOperatorName;
}