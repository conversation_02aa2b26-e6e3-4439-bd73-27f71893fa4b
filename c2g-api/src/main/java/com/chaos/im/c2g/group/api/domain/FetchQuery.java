package com.chaos.im.c2g.group.api.domain;

import com.chaos.keep.alive.common.persistent.domain.BaseQuery;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class FetchQuery extends BaseQuery {

    private Long chatId;

    private Long startMessageId;

    private Long stopMessageId;

    private String operatorNo;

    private String deviceId;

}
