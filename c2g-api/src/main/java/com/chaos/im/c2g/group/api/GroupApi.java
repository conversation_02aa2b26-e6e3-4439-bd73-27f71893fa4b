package com.chaos.im.c2g.group.api;

import com.chaos.im.c2g.group.api.domain.CreateGroupAndInviteReq;
import com.chaos.im.c2g.group.api.domain.GroupVO;
import com.chaos.im.c2g.group.api.domain.LastC2gMessageDTO;
import com.outstanding.framework.core.ResponseDTO;

import java.util.List;

public interface GroupApi {

    List<String> getMemberIdByGroupId(Long groupId);

    ResponseDTO<?> create(String ownerId , String name);

    ResponseDTO<?> create(String ownerId , String name, String ex,String faceUrl);

    ResponseDTO<?> dismiss(Long groupId);

    ResponseDTO<Long> createAndInvite(CreateGroupAndInviteReq req);

    ResponseDTO<?> findGroupByGroupId(Long groupId);

    ResponseDTO<?> kick(Long groupId, List<String> memberOperatorNos);

    ResponseDTO<?> invite(Long groupId, List<String> memberOperatorNos);

    ResponseDTO<?> findMemberByGroupId(Long groupId,Integer pageSize ,Integer current);

    LastC2gMessageDTO getLastMessageByChatIdAndOperatorNo(Long chatId, String operatorNo,String deviceId);


    GroupVO queryByOrderNo(String ownerId, String orderNo, Integer bizType);
}
