package com.chaos.im.c2g.group.api;

import com.chaos.im.c2g.group.api.domain.FetchQuery;
import com.chaos.im.c2g.group.api.domain.GroupMemberQuery;
import com.chaos.im.c2g.group.api.domain.MessageSendReq;
import com.outstanding.framework.core.ResponseDTO;

public interface C2gOpenApi {

    void sendMessage(MessageSendReq req) ;

    ResponseDTO<?> fetch(FetchQuery fetchQuery);

    ResponseDTO<?> listGroupMember( GroupMemberQuery query);
}
