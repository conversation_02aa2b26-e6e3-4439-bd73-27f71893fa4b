package com.chaos.im.c2g.group.api.domain;

import lombok.Data;

import java.io.Serializable;

@Data
public class MessageSendReq implements Serializable {
   // 显式声明版本ID（推荐）
   private static final long serialVersionUID = 1L;
   private Long messageId;
   private String fromOperatorNo;
   private String toOperatorNo;
   private Long chatId;
   private Integer chatType;
   private String content;
   private Integer category;
   private Long sequence;
   
}
