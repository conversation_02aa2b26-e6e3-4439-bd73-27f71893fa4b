package com.chaos.im.c2g.group.api.domain;

import com.chaos.keep.alive.common.core.domain.BaseDomain;
import lombok.Getter;
import lombok.Setter;


/**
 * <AUTHOR>
 */
@Getter
@Setter
public class GroupDTO extends BaseDomain {

    private Long id;

    /**
     * 群主id
     */
    private String ownerId;

    /**
     * 群名
     */
    private String name;

    private String faceUrl;

    private String ex;

    private Long chatId;

    private  Integer status;
}