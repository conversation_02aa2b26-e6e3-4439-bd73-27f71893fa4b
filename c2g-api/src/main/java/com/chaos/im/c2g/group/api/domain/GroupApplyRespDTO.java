package com.chaos.im.c2g.group.api.domain;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
public class GroupApplyRespDTO implements Serializable {
    private static final long serialVersionUID = 1834072644393669973L;

    /**
     * 申请单编号
     */
    private Long applyId;

    /**
     * 申请时间
     */
    private Date applyTime;

    /**
     * 申请场景
     */
    private String applyScene;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 申请原因
     */
    private String reason;

    /**
     * 群id
     */
    private String groupId;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 申请人操作员编号
     */
    private String operatorNo;

    /**
     * 申请人操作员类型
     */
    private Integer operatorType;

    /**
     * 申请人昵称
     */
    private String nickName;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 语言
     */
    private String language;

    /**
     * 受理人操作员编号
     */
    private String acceptOperatorNo;

    /**
     * 受理人名称
     */
    private String acceptOperatorName;

    /**
     * 受理时间
     */
    private Date acceptTime;

    /**
     * 处理人操作员编号
     */
    private String handleOperatorNo;

    /**
     * 处理人名称
     */
    private String handleOperatorName;

    /**
     * 处理时间
     */
    private Date handleTime;

    /**
     * 处理结果：10-已解决、11-未解决
     */
    private Integer handleResult;

    /**
     * 备注
     */
    private String remark;

    /**
     * 阅读状态：10-已读、11-未读
     */
    private Integer readStatus;
}