package com.chaos.im.c2g.group.api.domain;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class GroupApplyCountRespDTO implements Serializable {
    private static final long serialVersionUID = 3370683819501167972L;

    /**
     * 待处理数量
     */
    private Long pendingCount;

    /**
     * 处理中数量
     */
    private Long processingCount;
}
