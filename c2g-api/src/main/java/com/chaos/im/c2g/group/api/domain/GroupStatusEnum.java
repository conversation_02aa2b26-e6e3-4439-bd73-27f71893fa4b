package com.chaos.im.c2g.group.api.domain;

import com.outstanding.framework.core.BaseEnum;

public enum GroupStatusEnum implements BaseEnum<GroupStatusEnum, Integer> {
    NORMAL(10, "正常"),
    MUTE(11, "禁言"),
    DISMISS(12, "解散");

    GroupStatusEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    private Integer code;
    private String message;

    public Integer getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }
}
