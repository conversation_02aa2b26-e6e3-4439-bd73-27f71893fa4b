package com.chaos.keep.alive.client.http;

import com.chaos.keep.alive.client.tcp.TcpClient;
import com.chaos.keep.alive.client.ws.WebsocketClient;

/**
 * <AUTHOR>
 */
abstract class Remote {

    protected TcpClient tcpClient;

    protected WebsocketClient websocketClient;

    public Remote(TcpClient tcpClient) {
        this.tcpClient = tcpClient;
    }

    public Remote(WebsocketClient wsClient) {
        this.websocketClient = wsClient;
    }
}
