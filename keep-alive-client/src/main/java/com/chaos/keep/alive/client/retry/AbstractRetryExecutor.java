package com.chaos.keep.alive.client.retry;

import com.chaos.keep.alive.client.listener.CommandListener;
import com.chaos.keep.alive.common.protobuf.Command;

import java.util.concurrent.DelayQueue;

/**
 * <AUTHOR>
 */
abstract class AbstractRetryExecutor<T> implements RetryExecutor<T> {

    /**
     * 延迟队列
     */
    protected final DelayQueue<DelayTask<T>> queue = new DelayQueue<>();

    protected volatile boolean running;

    public abstract void start();

    public void stop() {
        running = false;
    }

    public abstract void enqueue(Command command, T data, CommandListener failedListener);
}
