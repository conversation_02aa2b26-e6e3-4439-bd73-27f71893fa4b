package com.chaos.keep.alive.client.tcp;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.chaos.keep.alive.client.IClient;
import com.chaos.keep.alive.client.config.PropertiesUtils;
import com.chaos.keep.alive.client.constant.ClientConstants;
import com.chaos.keep.alive.client.http.IpListRemote;
import com.chaos.keep.alive.client.listener.CommandListener;
import com.chaos.keep.alive.client.util.IdUtils;
import com.chaos.keep.alive.client.util.TokenHolder;
import com.chaos.keep.alive.common.core.constant.CommandType;
import com.chaos.keep.alive.common.core.constant.Constants;
import com.chaos.keep.alive.common.core.constant.MessageType;
import com.chaos.keep.alive.common.core.domain.BasePage;
import com.chaos.keep.alive.common.core.domain.JsonResult;
import com.chaos.keep.alive.common.core.domain.address.AddressInstance;
import com.chaos.keep.alive.common.core.exception.SystemException;
import com.chaos.keep.alive.common.core.util.AddressUtils;
import com.chaos.keep.alive.common.im.domain.MessageJsonPush;
import com.chaos.keep.alive.common.protobuf.*;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.PooledByteBufAllocator;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.util.concurrent.ScheduledFuture;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.chaos.keep.alive.client.util.HttpUtil.get;

/**
 * <AUTHOR>
 */
@Slf4j
public class TcpClient implements IClient {

    private static final EventLoopGroup threadGroup = new NioEventLoopGroup();

    private final IpListRemote ipListRemote = new IpListRemote(this);

    AtomicBoolean isConnected = new AtomicBoolean(false);

    private CommandListener commandListener;

    @Getter
    private AddressInstance addressInstance;

    private final int CONNECT_MAX_RETRY = PropertiesUtils.getConnectMaxRetry() == -1 ? Integer.MAX_VALUE : PropertiesUtils.getConnectMaxRetry();

    ScheduledFuture<?> scheduledFuture;

    ChannelHandlerContext ctx;

//    RetryCommandExecutor retryCommandExecutor;
//
//    RetryMessageExecutor retryMessageExecutor;

    MessageSet messageSet;

    SocketChannel socketChannel;


    public TcpClient() {
        this.messageSet = new MessageSet();
//        this.retryCommandExecutor = new RetryCommandExecutor(this);
//        this.retryMessageExecutor = new RetryMessageExecutor(this);
    }

    @Override
    public void connect(String token, String operatorNo, String deviceId, String appId, Integer appNo, CommandListener commandListener) {
        this.commandListener = commandListener;
        TokenHolder.TokenInfo tokenInfo = new TokenHolder.TokenInfo(token, operatorNo, deviceId, appId);
        TokenHolder.setTokenInfo(this, tokenInfo);
        reconnect();

    }

    public boolean isConnected() {
        return isConnected.get();
    }

    @SneakyThrows
    void reconnect() {
        Bootstrap client = create();
        int retry = 0;
        while (retry < CONNECT_MAX_RETRY) {
            try {

                if (StrUtil.isNotBlank(PropertiesUtils.getKaGatewayAddress())) {
                    addressInstance = AddressUtils.parseAddress(PropertiesUtils.getKaGatewayAddress());
                } else {
                    //调用iplist服务获取gateway的ip进行连接
                    JsonResult<?> jsonResult = ipListRemote.get();
                    if (jsonResult.getSuccess()) {
                        addressInstance = JSONUtil.toBean((JSONObject) jsonResult.getData(), AddressInstance.class);
                    } else throw new SystemException(jsonResult.getErrorMessage());
                }
                // 连接网关服务
                ChannelFuture channelFuture = client.connect(addressInstance.getIp(), addressInstance.getPort()).sync();
                if (channelFuture.isSuccess()) {
                    log.info("与网关[{}]的连接已建立", addressInstance.getServerId());
                    // 注册客户端到网关服务
                    online((SocketChannel) channelFuture.channel());
                    return;
                }

            } catch (Exception e) {
                log.error("网关连接失败重试", e);
                Thread.sleep(3000);
                retry++;
            }
        }
        throw new SystemException("与网关建立连接失败");
    }


    @Override
    public void sendMessage(Command command){
        writeAndFlushCommand(command);
    }

    /**
     * 发送命令
     */
    @SneakyThrows
    private void writeAndFlushCommand(Command command) {
        socketChannel.writeAndFlush(command);
    }

    @SneakyThrows
    public void writeAndFlush(Object msg) {
        socketChannel.writeAndFlush(msg);
    }




    @SneakyThrows
    private void doFetchOfflineMessage(Long chatId, Integer chatType, Long size, Long current, String operatorNo, String deviceId) {

        String lastAckUrl ;
        String fetchOfflineMessageUrl;
        if (Objects.equals(chatType, com.chaos.keep.alive.common.im.constant.Constants.CHAT_TYPE_C2C)) {
            lastAckUrl = PropertiesUtils.getFetchC2cLastAckUrl();

            fetchOfflineMessageUrl = PropertiesUtils.getFetchC2cOfflineMessageUrl();
        } else {
            lastAckUrl = PropertiesUtils.getFetchC2gLastAckUrl();
            fetchOfflineMessageUrl = PropertiesUtils.getFetchC2gOfflineMessageUrl();
        }


        java.util.Map<String, String> queryParams = new java.util.HashMap<>();
        queryParams.put("chatId", chatId + "");
        queryParams.put("size", size + "");
        queryParams.put("current", current + "");
        queryParams.put("operatorNo", operatorNo);
        queryParams.put("deviceId", deviceId);


        String lastAckResponse = get(lastAckUrl, queryParams, null);

        JsonResult<Long> lastjsonResult = JSON.parseObject(lastAckResponse, new com.alibaba.fastjson.TypeReference<JsonResult<Long>>() {
        });

        // 获取 data 字段
        Long lastdata = lastjsonResult.getData();

        queryParams.put("startMessageId", lastdata + "");

        String response = get(fetchOfflineMessageUrl, queryParams, null);


        // 解析整个响应为 JsonResult
        JsonResult<com.alibaba.fastjson.JSONObject> jsonResult = JSON.parseObject(response, new com.alibaba.fastjson.TypeReference<JsonResult<com.alibaba.fastjson.JSONObject>>() {
        });

        // 获取 data 字段
        com.alibaba.fastjson.JSONObject data = jsonResult.getData();
        if (data == null) {
            log.info("{} 拉取到离线消息为空", TokenHolder.getTokenInfo(this).getOperatorNo());
            return;
        }
        // 解析 data 字段为 BasePage
        BasePage<MessageJsonPush> page = data.toJavaObject(new com.alibaba.fastjson.TypeReference<BasePage<MessageJsonPush>>() {
        });

        log.info("拉取到离线消息总数:{}  , size:{}, current:{} ,chatId:{},chatType:{}", page.getPagination().getTotal(), size, page.getPagination().getCurrent(), chatId, chatType);

        for (MessageJsonPush messageJsonPush : page.getList()) {
            log.info("拉取到一条离线消息 content:{} fromOperatorNo:{} chatId:{} chatType:{} messageId:{}", messageJsonPush.getContent(), messageJsonPush.getFromOperatorNo(),
                    messageJsonPush.getChatId(), messageJsonPush.getChatType(),messageJsonPush.getMessageId());
            sendC2gAck(messageJsonPush.getChatId(), messageJsonPush.getMessageId(), messageJsonPush.getChatType());
        }

        if (page.getPagination().getTotal() > page.getPagination().getPageSize() * page.getPagination().getCurrent()) {
            fetchC2gOfflineMessage(chatId, size, page.getPagination().getCurrent() + 1, operatorNo, deviceId);
        }

    }


    @SneakyThrows
    @Override
    public void fetchC2cOfflineMessage(Long chatId, Long size, Long current, String operatorNo, String deviceId) {

        doFetchOfflineMessage(chatId, 1, size, current, operatorNo, deviceId);

    }

    @SneakyThrows
    @Override
    public void fetchC2gOfflineMessage(Long chatId, Long size, Long current, String operatorNo, String deviceId) {


        doFetchOfflineMessage(chatId, 2, size, current, operatorNo, deviceId);

    }


    @SneakyThrows
    @Override
    public void sendC2cAck(Long chatId, String toOperatorNo, Long messageId, Integer chatType) {
        C2cMessageAckRequest request = C2cMessageAckRequest.newBuilder()
                .setOperatorNo(TokenHolder.getTokenInfo(this).getOperatorNo())
                .setMessageId(messageId)
                .setChatType(com.chaos.keep.alive.common.im.constant.Constants.CHAT_TYPE_C2C)
                .setDeviceId(TokenHolder.getTokenInfo(this).getDeviceId())
                .setChatId(chatId)
                .build();

        Command command = Command.newBuilder()
                .setOperatorNo(TokenHolder.getTokenInfo(this).getOperatorNo())
                .setDeviceId(TokenHolder.getTokenInfo(this).getDeviceId())
                .setBizType(com.chaos.keep.alive.common.im.constant.CommandType.COMMAND_IM_C2C_MESSAGE_SEND)
                .setMessageType(com.chaos.keep.alive.common.im.constant.MessageType.MESSAGE_TYPE_CLIENT_ACK)
                .setBody(request.toByteString())
                .build();
        log.info("{}发送单聊ACK,toOperatorNo:{},messageId:{},chatType:{}", command.getOperatorNo(), toOperatorNo, messageId, chatType);
        sendMessage(command);

    }

    @SneakyThrows
    @Override
    public void sendC2gAck(Long chatId, Long messageId, Integer chatType) {
        C2gMessageAckRequest request = C2gMessageAckRequest.newBuilder()
                .setOperatorNo(TokenHolder.getTokenInfo(this).getOperatorNo())
                .setChatId(chatId)
                .setMessageId(messageId)
                .setChatType(2)
                .setDeviceId(TokenHolder.getTokenInfo(this).getDeviceId())
                .build();

        Command command = Command.newBuilder()
                .setOperatorNo(TokenHolder.getTokenInfo(this).getOperatorNo())
                .setDeviceId(TokenHolder.getTokenInfo(this).getDeviceId())
                .setBizType(com.chaos.keep.alive.common.im.constant.CommandType.COMMAND_IM_C2G_MESSAGE_SEND)
                .setMessageType(com.chaos.keep.alive.common.im.constant.MessageType.MESSAGE_TYPE_CLIENT_ACK)
                .setBody(request.toByteString())
                .build();
        log.info("{}发送群聊ACK,chatId:{},messageId:{},chatType:{}", command.getOperatorNo(), chatId, messageId, chatType);
        sendMessage(command);

    }

    @Override
    public void sendText(Long chatId, String toOperatorNo,
                         String content, Integer category){
        log.info("发送Text content:{}",content);
        socketChannel.writeAndFlush(content);
    }

    @SneakyThrows
    @Override
    public void sendC2cMessage(Long chatId, String toOperatorNo,
                               String content, Integer category) {
        C2cMessageSendRequest request = C2cMessageSendRequest.newBuilder()
                .setFromOperatorNo(TokenHolder.getTokenInfo(this).getOperatorNo())
                .setToOperatorNo(toOperatorNo)
                .setContent(content)
                .setCategory(category)
                .setChatId(chatId)
                .setMessageId(IdUtils.getId("message"))
                .build();

        Command command = Command.newBuilder()
                .setOperatorNo(TokenHolder.getTokenInfo(this).getOperatorNo())
                .setDeviceId(TokenHolder.getTokenInfo(this).getDeviceId())
                .setBizType(com.chaos.keep.alive.common.im.constant.CommandType.COMMAND_IM_C2C_MESSAGE_SEND)
                .setMessageType(MessageType.MESSAGE_TYPE_SEND)
                .setBody(request.toByteString())
                .build();
        log.info("{}发送单聊消息给operatorNo:{},content:{},category:{}", command.getOperatorNo(), toOperatorNo, content, category);
        sendMessage(command);

    }

    @SneakyThrows
    @Override
    public void sendC2gMessage(Long chatId,
                               String content, Integer category) {
        C2gMessageSendRequest request = C2gMessageSendRequest.newBuilder()
                .setFromOperatorNo(TokenHolder.getTokenInfo(this).getOperatorNo())
                .setChatId(chatId)
                .setContent(content)
                .setCategory(category)
                .setMessageId(IdUtils.getId("message"))
                .build();

        Command command = Command.newBuilder()
                .setOperatorNo(TokenHolder.getTokenInfo(this).getOperatorNo())
                .setDeviceId(TokenHolder.getTokenInfo(this).getDeviceId())
                .setBizType(com.chaos.keep.alive.common.im.constant.CommandType.COMMAND_IM_C2G_MESSAGE_SEND)
                .setMessageType(MessageType.MESSAGE_TYPE_SEND)
                .setBody(request.toByteString())
                .build();
        log.info("{}发送群聊消息到chatId:{},content:{},category:{}", command.getOperatorNo(), chatId, content, category);
        sendMessage(command);

    }

    @SneakyThrows
    @Override
    public void reportRiderLocation(long riderId, String appId,
                                    Long timestamp, Double latitude, Double longitude) {

        RiderReportLocationRequest.Builder builder = RiderReportLocationRequest.newBuilder();
        String messageId = UUID.randomUUID().toString().replace("-", "");
        builder.setRiderId(riderId)
                .setLatitude(latitude)
                .setLongitude(longitude);

        RiderReportLocationRequest body = builder.build();

        String deviceId = TokenHolder.getTokenInfo(this).getDeviceId();

        String operatorNo = TokenHolder.getTokenInfo(this).getOperatorNo();

        Command command = Command.newBuilder()
                .setAppSdkVersion(Constants.APP_SDK_VERSION)
                .setMessageType(MessageType.MESSAGE_TYPE_SEND)
                .setBizType(CommandType.COMMAND_WOWNOW_RIDER_REPORT_LOCATION)
                .setDeviceId(deviceId)
                .setOperatorNo(operatorNo)
                .setAppId(appId)
                .setTimestamp(timestamp)
                .setBody(body.toByteString())
                .build();
        sendMessage(command);
        log.info("客户端[{},{}]上报骑手位置 riderId:{} , latitude:{},longitude:{}", operatorNo, deviceId,
                riderId, latitude, longitude);
    }

    @SneakyThrows
    @Override
    public void reportLocation(long driverId, String appId,
                               Long timestamp, Double latitude, Double longitude, Double speed, Integer direction, String orderId) {

        ReportLocationRequest.Builder builder = ReportLocationRequest.newBuilder();
        String messageId = UUID.randomUUID().toString().replace("-", "");
        builder.setDriverId(driverId)
                .setLatitude(latitude)
                .setLongitude(longitude)
                .setDirection(direction)
                .setOrderId(orderId)
                .setSpeed(speed);

        ReportLocationRequest body = builder.build();

        String deviceId = TokenHolder.getTokenInfo(this).getDeviceId();

        String operatorNo = TokenHolder.getTokenInfo(this).getOperatorNo();

        Command command = Command.newBuilder()
                .setAppSdkVersion(Constants.APP_SDK_VERSION)
                .setMessageType(MessageType.MESSAGE_TYPE_SEND)
                .setBizType(CommandType.COMMAND_GONOW_DRIVER_REPORT_LOCATION)
                .setDeviceId(deviceId)
                .setOperatorNo(operatorNo)
                .setAppId(appId)
                .setTimestamp(timestamp)
                .setBody(body.toByteString())
                .build();
        sendMessage(command);
        log.info("客户端[{},{}]上报司机位置 driverId:{} , latitude:{},longitude:{},speed:{},direction:{},timestamp:{},orderId:{}", operatorNo, deviceId,
                driverId, latitude, longitude, speed, direction, timestamp, orderId);
    }

    /**
     * 上报位置
     */
//    public void reportLocationCommand(Command command, CommandListener failedListener) {
//        try {
//            // 发送命令
//            reportLocationCommand(command);
//            if (command.getBizType() == CommandType.COMMAND_GONOW_DRIVER_REPORT_LOCATION) {
//
//                ReportLocationRequest request = ReportLocationRequest.parseFrom(command.getBody());
////                MessageSendRequest request = MessageSendRequest.parseFrom(command.getBody());
//                if (messageSet.exist(request.getMessageId())) {
//                    log.info("重新发送消息[messageId:{},bizType:{},latitude:{},longitude:{}]", request.getMessageId(), command.getBizType(), request.getLatitude(), request.getLongitude());
//                }
//                // 本地缓存客户端消息id
//                messageSet.setMessage(request.getMessageId());
//                // 放入消息重试队列
//                retryMessageExecutor.enqueue(command, request.getMessageId(), failedListener);
//            }
//        } catch (Exception e) {
//            retryCommandExecutor.enqueue(command, null, failedListener);
//        }
//    }
    public void close() {
//        retryCommandExecutor.stop();
//        retryMessageExecutor.stop();
        if (Objects.nonNull(scheduledFuture) && scheduledFuture.isCancelled()) {
            scheduledFuture.cancel(true);
        }
        if (Objects.nonNull(ctx)) {
            ctx.close();
            ctx = null;
        }
//        threadGroup.shutdownGracefully();
    }

    /**
     * 处理网关服务发送来的命令
     */
    void handleServerCommand(Command command) {
        commandListener.onCommand(command);
    }

    public boolean waitAck(String messageId) {
        return messageSet.exist(messageId);
    }

    /**
     * 创建Bootstrap
     */
    private Bootstrap create() {
        Bootstrap client = new Bootstrap();
        client.group(threadGroup)
                .channel(NioSocketChannel.class)
                .option(ChannelOption.ALLOCATOR, PooledByteBufAllocator.DEFAULT)
                .option(ChannelOption.TCP_NODELAY, true)
                .option(ChannelOption.SO_KEEPALIVE, true)
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 10000)
                .handler(new TcpClientChannelInitializer(this));
        return client;
    }

    /**
     * 连接成功后向网关服务端发送上线消息
     */
    private void online(SocketChannel socketChannel) {
        String token = TokenHolder.getTokenInfo(this).getToken();
        String operatorNo = TokenHolder.getTokenInfo(this).getOperatorNo();
        String deviceId = TokenHolder.getTokenInfo(this).getDeviceId();

        OnlineRequest body = OnlineRequest.newBuilder().setToken(token).build();

        Command newCommand = Command.newBuilder()
                .setAppSdkVersion(Constants.APP_SDK_VERSION)
                .setMessageType(MessageType.MESSAGE_TYPE_SEND)
                .setBizType(CommandType.COMMAND_ONLINE)
                .setTimestamp(System.currentTimeMillis())
                .setOperatorNo(operatorNo)
                .setDeviceId(deviceId)
                .setBody(body.toByteString())
                .build();
        socketChannel.writeAndFlush(newCommand);
        this.socketChannel = socketChannel;
        log.info("[{}:{}]通知网关[{}]上线...", operatorNo, ClientConstants.DEVICE_ID, addressInstance.getServerId());
    }

    static class MessageSet {

        private final Set<String> messageSet = Collections.synchronizedSet(new HashSet<>());

        void setMessage(String messageId) {
            messageSet.add(messageId);
        }

        void clearMessage(String messageId) {
            messageSet.remove(messageId);
        }

        Boolean exist(String messageId) {
            return messageSet.contains(messageId);
        }
    }
}
