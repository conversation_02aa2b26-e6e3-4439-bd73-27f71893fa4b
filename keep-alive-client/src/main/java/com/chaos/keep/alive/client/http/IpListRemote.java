package com.chaos.keep.alive.client.http;

import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.chaos.keep.alive.client.config.PropertiesUtils;
import com.chaos.keep.alive.client.tcp.TcpClient;
import com.chaos.keep.alive.client.util.TokenHolder;
import com.chaos.keep.alive.client.ws.WebsocketClient;
import com.chaos.keep.alive.common.core.domain.JsonResult;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class IpListRemote extends Remote {

    private final static String URL = PropertiesUtils.getWebGatewayUrl() + "/api/iplist";

    public IpListRemote(TcpClient tcpClient) {
        super(tcpClient);
    }

    public IpListRemote(WebsocketClient tcpClient) {
        super(tcpClient);
    }

    public JsonResult<?> get() {
        String token = TokenHolder.getTokenInfo(tcpClient).getToken();
        Map<String, Object> params = new HashMap<>();
        String jsonStr = HttpRequest.get(URL)
                .bearerAuth(token)
                .form(params)
                .execute().body();
        return JSONUtil.toBean(jsonStr, JsonResult.class);
    }
}
