package com.chaos.keep.alive.client.util;

import com.chaos.keep.alive.client.IClient;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class TokenHolder {

    private final static Map<IClient, TokenInfo> tokenInfoMap = new ConcurrentHashMap<>();

    public static TokenInfo getTokenInfo(IClient iClient) {
        return tokenInfoMap.get(iClient);
    }

    public static void setTokenInfo(IClient client, TokenInfo tokenInfo) {
        TokenHolder.tokenInfoMap.put(client, tokenInfo);
    }

    @Getter
    @Setter
    @AllArgsConstructor
    public static class TokenInfo {

        private String token;

        private String operatorNo;

        private String deviceId;

        private String appId;

    }

}
