package com.chaos.keep.alive.client.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Properties;

public class PropertiesUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(PropertiesUtils.class);

    private static final String WEB_GATEWAY_URL = "web.gateway.url";

    private static final String ID_SERVER_URL = "id.server.url";

    private static final String ID_CACHE_COUNT = "id.cache.count";

    private static final String HEARTBEAT_INTERVAL = "heartbeat.interval";

    private static final String CONNECT_MAX_RETRY = "connect.max.retry";

    private static final String FETCH_C2G_OFFLINE_MESSAGE_URL = "fetch.c2g.offline.message.url";

    private static final String FETCH_C2G_LAST_ACK =   "fetch.c2g.last.ack.url";

    private static final String FETCH_C2C_OFFLINE_MESSAGE_URL = "fetch.c2c.offline.message.url";

    private static final String FETCH_C2C_LAST_ACK = "fetch.c2c.last.ack.url";

    private static final String COMMAND_MAX_RETRY = "command.max.retry";

    private static final String MESSAGE_ACK_WAIT_TIME = "message.ack.waitTime";

    private static final String KA_GATEWAY_ADDRESS = "ka.gateway.address";
    private static final String KA_GATEWAY_WS_ADDRESS = "ka.gateway.ws.address";

    private static final String LOG_LEVEL = "logging.level";

    private static final String LOG_PACKAGE = "logging.package";

    private static final String PROP_PATH = "/config.properties";

    private static Properties prop;

    static {
        try {
            prop = new Properties();
            File file = new File("conf" + PROP_PATH);
            if (file.exists()) {
                prop.load(new FileInputStream(file));
            } else {
                InputStream in = PropertiesUtils.class.getResourceAsStream(PROP_PATH);
                prop.load(in);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    public static String getWebGatewayUrl() {
        return prop.getProperty(WEB_GATEWAY_URL);
    }

    public static String getIdServerUrl() {
        return prop.getProperty(ID_SERVER_URL,"http://localhost:8100");
    }

    public static int getIdCacheCount() {
        return Integer.parseInt(prop.getProperty(ID_CACHE_COUNT,"2000"));
    }

    public static String getFetchC2gOfflineMessageUrl() {
        return prop.getProperty(FETCH_C2G_OFFLINE_MESSAGE_URL);
    }

    public static String getFetchC2gLastAckUrl() {
        return prop.getProperty(FETCH_C2G_LAST_ACK);
    }

    public static String getFetchC2cOfflineMessageUrl() {
        return prop.getProperty(FETCH_C2C_OFFLINE_MESSAGE_URL);
    }

    public static String getFetchC2cLastAckUrl() {
        return prop.getProperty(FETCH_C2C_LAST_ACK);
    }

    public static int getHeartbeatInterval() {
        return Integer.parseInt(prop.getProperty(HEARTBEAT_INTERVAL));
    }

    public static int getConnectMaxRetry() {
        return Integer.parseInt(prop.getProperty(CONNECT_MAX_RETRY));
    }

    public static int getCommandMaxRetry() {
        return Integer.parseInt(prop.getProperty(COMMAND_MAX_RETRY));
    }

    public static int getMessageAckWaitTime() {
        return Integer.parseInt(prop.getProperty(MESSAGE_ACK_WAIT_TIME));
    }

    public static String getKaGatewayAddress() {
        return prop.getProperty(KA_GATEWAY_ADDRESS);
    }

    public static String getKaGatewayWsAddress(){
        return prop.getProperty(KA_GATEWAY_WS_ADDRESS);
    }

    public static String getLogLevel() {
        return prop.getProperty(LOG_LEVEL, "info");
    }

    public static String getLogPackage() {
        return prop.getProperty(LOG_PACKAGE, "com.chaos.keep.alive.client");
    }

}

