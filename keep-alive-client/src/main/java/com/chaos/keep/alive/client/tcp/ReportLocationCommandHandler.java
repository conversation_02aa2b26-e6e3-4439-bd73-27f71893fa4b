package com.chaos.keep.alive.client.tcp;

import com.chaos.keep.alive.client.config.PropertiesUtils;
import com.chaos.keep.alive.client.constant.ClientConstants;
import com.chaos.keep.alive.common.core.constant.CommandType;
import com.chaos.keep.alive.common.protobuf.Command;
import com.chaos.keep.alive.common.protobuf.Result;
import io.netty.channel.ChannelHandlerContext;
import io.netty.util.concurrent.ScheduledFuture;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
public class ReportLocationCommandHandler extends AbstractCommandHandler {

    private final int heartbeatInterval = PropertiesUtils.getHeartbeatInterval();

    public ReportLocationCommandHandler(ThreadPoolExecutor executor) {
        super(executor);
    }

    @Override
    @SneakyThrows
    public void handleCommand(Command command, ChannelHandlerContext ctx, TcpClient tcpClient) {
        tcpClient.handleServerCommand(command);
    }

    @Override
    public int getType() {
        return CommandType.COMMAND_ONLINE;
    }
}
