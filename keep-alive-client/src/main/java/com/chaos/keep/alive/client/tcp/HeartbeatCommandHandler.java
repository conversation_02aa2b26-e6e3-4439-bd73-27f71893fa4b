package com.chaos.keep.alive.client.tcp;

import com.chaos.keep.alive.common.core.constant.CommandType;
import com.chaos.keep.alive.common.protobuf.Command;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 */
@Slf4j
public class Heart<PERSON><PERSON>om<PERSON><PERSON>and<PERSON> extends AbstractCommandHandler {

    public HeartbeatCommandHandler(ThreadPoolExecutor executor) {
        super(executor);
    }

    @Override
    public void handleCommand(Command command, ChannelHandlerContext ctx, TcpClient tcpClient) {
        log.debug("收到[{}]的心跳响应", tcpClient.getAddressInstance().getServerId());
    }

    @Override
    public int getType() {
        return CommandType.COMMAND_HEARTBEAT;
    }
}
