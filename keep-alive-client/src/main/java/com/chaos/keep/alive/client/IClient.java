package com.chaos.keep.alive.client;

import com.chaos.keep.alive.client.listener.CommandListener;
import com.chaos.keep.alive.common.protobuf.Command;

public interface IClient {
    void connect(String mockToken, String mockOperatorNo, String mockDeviceId, String mockAppId,
                 Integer mockAppNo, CommandListener commandListener);

    void sendMessage(Command command);

    void fetchC2cOfflineMessage(Long chatId, Long size, Long current, String operatorNo, String deviceId);

    void fetchC2gOfflineMessage(Long chatId, Long size, Long current, String operatorNo, String deviceId);

    void sendC2cAck(Long chatId, String toOperatorNo, Long messageId, Integer chatType);

    void sendC2gAck(Long chatId, Long messageId, Integer chatType);

    void sendText(Long chatId, String toOperatorNo,
                  String content, Integer category);

    void sendC2cMessage(Long chatId, String toOperatorNo,
                        String content, Integer category);

    void sendC2gMessage(Long chatId,
                        String content, Integer category);

    boolean isConnected();

    void reportRiderLocation(long riderId, String appId,
                             Long timestamp, Double latitude, Double longitude);

    void reportLocation(long driverId, String appId,
                        Long timestamp, Double latitude, Double longitude, Double speed, Integer direction, String orderId);
}
