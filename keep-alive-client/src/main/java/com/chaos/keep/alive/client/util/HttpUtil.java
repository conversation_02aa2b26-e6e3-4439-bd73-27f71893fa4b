package com.chaos.keep.alive.client.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.chaos.keep.alive.common.core.domain.BasePage;
import com.chaos.keep.alive.common.core.domain.JsonResult;
import com.chaos.keep.alive.common.im.domain.MessageJsonPush;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
public class HttpUtil {

    private static final OkHttpClient client = new OkHttpClient();


    /**
     * 发起一个HTTP GET请求
     *
     * @param url         请求的URL
     * @param queryParams 查询参数
     * @param headers     请求头
     * @return 响应体的字符串形式
     * @throws IOException 如果请求过程中发生IO异常
     */
    public static String get(String url, java.util.Map<String, String> queryParams, java.util.Map<String, String> headers) throws IOException {
        // 构建带有查询参数的URL
        HttpUrl.Builder urlBuilder = HttpUrl.parse(url).newBuilder();
        if (queryParams != null) {
            for (java.util.Map.Entry<String, String> entry : queryParams.entrySet()) {
                urlBuilder.addQueryParameter(entry.getKey(), entry.getValue());
            }
        }
        HttpUrl httpUrl = urlBuilder.build();

        // 构建请求对象
        Request.Builder requestBuilder = new Request.Builder()
                .url(httpUrl);

        // 添加请求头
        if (headers != null) {
            for (java.util.Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.addHeader(entry.getKey(), entry.getValue());
            }
        }

        Request request = requestBuilder.build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            return response.body().string();
        }
    }

    public static void main(String[] args) throws IOException {

        String url = "http://127.0.0.1:8096/c2g/api/c2g/fetch";

        java.util.Map<String, String> queryParams = new java.util.HashMap<>();
        queryParams.put("chatId", "1");
        queryParams.put("size", "10");
        queryParams.put("operatorNo", "1881984098591682560");
        queryParams.put("deviceId", "E1FBA8B0B784ACBE15FB0F18F81CBE131E76608B");


        String response = get(url, queryParams, null);

        // 解析整个响应为 JsonResult
        JsonResult<JSONObject> jsonResult = JSON.parseObject(response, new com.alibaba.fastjson.TypeReference<JsonResult<JSONObject>>() {
        });

        // 获取 data 字段
        JSONObject data = jsonResult.getData();
        // 解析 data 字段为 BasePage
        BasePage<MessageJsonPush> pushList = data.toJavaObject(new com.alibaba.fastjson.TypeReference<BasePage<MessageJsonPush>>() {
        });


        // 打印结果
//            System.out.println("Data: " + jsonResult.getData());
//            System.out.println("MessageJsonPush List: " + pushList.getList());

    }
}
