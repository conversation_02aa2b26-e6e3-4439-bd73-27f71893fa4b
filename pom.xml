<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <version>*******-SNAPSHOT</version>
    <groupId>com.chaos</groupId>
    <artifactId>keep-alive-con</artifactId>
    <packaging>pom</packaging>

    <parent>
        <groupId>com.outstanding</groupId>
        <artifactId>go-framework-parent</artifactId>
        <version>3.3.6-SNAPSHOT</version>
    </parent>

    <modules>
        <module>gateway-tcp</module>
        <module>keep-alive-common</module>
        <module>route</module>
        <module>iplist</module>
        <module>keep-alive-client</module>
        <module>keep-alive-client-mock</module>
        <module>driver-trajectory</module>
        <module>c2g</module>
        <module>chat</module>
        <module>chat-api</module>
        <module>c2g-api</module>
        <module>id-server</module>
        <module>c2c</module>
        <module>c2c-api</module>
        <module>id-server-api</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <mybatis.plus.version>3.4.1</mybatis.plus.version>
        <druid.boot.version>1.2.8</druid.boot.version>
        <protobuf.version>3.19.1</protobuf.version>
        <netty.version>4.1.78.Final</netty.version>
        <go.framework.version>3.4.0-SNAPSHOT</go.framework.version>

    </properties>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>2021.0.6</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>


            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-framework</artifactId>
                <version>5.1.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>5.1.0</version>
            </dependency>

            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${netty.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.26</version>
            </dependency>

            <dependency>
                <groupId>com.outstanding</groupId>
                <artifactId>framework-base</artifactId>
                <version>${go.framework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.outstanding</groupId>
                <artifactId>framework-core</artifactId>
                <version>${go.framework.version}</version>
                <optional>true</optional>
            </dependency>

            <dependency>
                <groupId>com.outstanding</groupId>
                <artifactId>framework-container-dubbo</artifactId>
                <version>${go.framework.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>