package com.chaos.im.chat.test;

import com.chaos.im.chat.ChatApplication;
import com.chaos.im.chat.domain.*;
import com.chaos.im.chat.service.ChatOpenApiService;
import com.chaos.im.chat.service.ChatService;
import com.chaos.im.chat.service.CustomerApplyService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.Date;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ChatApplication.class)
public class ImCustomerTest {

    @Autowired
    private ChatService chatService;

    @Autowired
    private CustomerApplyService customerApplyService;

    @Autowired
    private ChatOpenApiService chatOpenApiService;


    /**
     * 创建群聊会话
     */
    @Test
    public void testCreateGroupChat(){
        CreateGroupChatRequest createChatRequest = new CreateGroupChatRequest();
        createChatRequest.setChatType(2);
        createChatRequest.setNickname("test");
        createChatRequest.setAvatar("http://www.baidu.com");
        createChatRequest.setOperatorNo("1278310923422830592");
        createChatRequest.setMemberIds(Arrays.asList("1278310923422830592"));
        chatService.createGroupChat(createChatRequest);
    }


    /**
     * 创建客服申请单
     */
    @Test
    public void testCustomerApply(){
        CustomerApplyDO customerApplyDO = new CustomerApplyDO();
        customerApplyDO.setApplyTime(new Date());
        customerApplyDO.setLang("en-US");
        customerApplyDO.setChatId(129L);
        customerApplyDO.setHeadUrl("http://www.baidu.com");
        customerApplyDO.setOrderNo("11111");
        customerApplyDO.setOrderTime(new Date());
        customerApplyDO.setType(1);
        customerApplyDO.setUserMobile("110");
        customerApplyDO.setUserName("userName");
        customerApplyDO.setUserOperatorNo("1278310923422830592");
        customerApplyDO.setReceiverName("test");
        customerApplyDO.setReceiverMobile("120");
        customerApplyService.save(customerApplyDO);
    }

    /**
     * 客服申请单查询
     */
    @Test
    public void selectApplyPage(){
        PageCustomerApplyReq req = new PageCustomerApplyReq();
        req.setCustomerNo("");
//        chatOpenApiService.page();
    }

    /**
     * 客服介入群聊
     */
    @Test
    public void acceptApply(){
        AcceptApplyReq reqDTO = new AcceptApplyReq();
        reqDTO.setApplyId(10L);
//        reqDTO.setCustomerNo("O1551838355870466048");
        reqDTO.setCustomerNo("O1848661095134892032");
        customerApplyService.acceptCustomerApply(reqDTO);
    }

    /**
     * 客服更新订单阅读状态
     */
    @Test
    public void updateReadStatus(){
        UpdateOrderApplyStatusReq req = new UpdateOrderApplyStatusReq();
        req.setOrderNo("11111");
        req.setStatus(1);
        chatOpenApiService.updateReadStatus(req);
    }


    /**
     * 客服
     */
    @Test
    public void updateHandleStatus(){

    }
}
