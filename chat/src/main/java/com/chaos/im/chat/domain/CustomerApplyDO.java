package com.chaos.im.chat.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chaos.keep.alive.common.persistent.domain.BaseDO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@TableName("customer_apply")
@Getter
@Setter
public class CustomerApplyDO extends BaseDO {
    private String orderNo;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderTime;

    private String userName;
    private String userMobile;
    private String receiverName;
    private String receiverMobile;
    private String headUrl;
    private String merchantNo;
    private String merchantName;
    private String merchantMobile;
    private String merchantHeadUrl;
    private String userOperatorNo;
    private String riderOperatorNo;
    private String riderMobile;
    private String riderName;
    private String riderHeadUrl;
    private Integer type;
    private String lang;
    private Integer readStatus ;
    private Integer interveneStatus ;
    private Integer handleStatus ;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyTime;

    private Long chatId;


}
