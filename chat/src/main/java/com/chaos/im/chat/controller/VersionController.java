package com.chaos.im.chat.controller;

import cn.hutool.core.comparator.VersionComparator;
import com.chaos.im.chat.domain.ChatMemberList;
import com.chaos.im.chat.domain.ReportAppVersionRequest;
import com.chaos.im.chat.domain.UserAppVersionDO;
import com.chaos.im.chat.service.UserAppVersionService;
import com.chaos.usercenter.api.UserOperatorFacade;
import com.chaos.usercenter.api.dto.req.UserDeviceInfoGetReqDTO;
import com.chaos.usercenter.api.dto.resp.UserDeviceForImRespDTO;
import com.outstanding.framework.core.ResponseDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/version")
public class VersionController {

    @Autowired
    private UserOperatorFacade userOperatorFacade;

    @Autowired
    private UserAppVersionService userAppVersionService;

    @Value("${wownow.im.superapp.android.version:2.75.15}")
    private String wownowImSuperAppAndroidVersion;

    @Value("${wownow.im.c2g.superapp.android.version:2.75.15}")
    private String wownowImC2gSuperAppAndroidVersion;

    @Value("${wownow.im.superapp.ios.version:5.75.15}")
    private String wownowImSuperAppIOSVersion;

    @Value("${wownow.im.c2g.superapp.ios.version:5.75.15}")
    private String wownowImC2gSuperAppIOSVersion;

    @Value("${wownow.im.driver.android.version:1.2.8}")
    private String wownowImDriverAndroidVersion ;

    @Value("${wownow.im.driver.ios.version:5.2.8}")
    private String wownowImDriverIosVersion ;

    @Value("${wownow.im.delivery.android.version:5.2.8}")
    private String wownowImDeliveryAndroidVersion;

    @Value("${wownow.im.delivery.ios.version:5.2.8}")
    private String wownowImDeliveryIosVersion;

    private final String DEVICE_TYPE_ANDROID="ANDROID";
    private final String DEVICE_TYPE_IOS="IOS";

    private final String APP_ID_DRIVER="GoNowDriver";
    private final String APP_ID_SUPPER="SuperApp";
    private final String APP_ID_Delivery="Delivery";


    @GetMapping("/get")
    public String testGetVersion(String key) {
        if(key.equals("wownow.im.superapp.ios.version")){
            return wownowImSuperAppIOSVersion;
        } else if (key.equals("wownow.im.driver.ios.version")) {
            return wownowImDriverIosVersion;
        }else if(key.equals("wownow.im.driver.android.version")){
            return wownowImDriverIosVersion;
        }else if(key.equals("wownow.im.superapp.android.version")){
            return wownowImDriverIosVersion;
        }else{
            return null;
        }
    }

    @PostMapping("/reportAppInfo")
    public ResponseDTO<?> reportAppVersion(@RequestBody ReportAppVersionRequest reportAppVersionRequest){

        userAppVersionService.save(reportAppVersionRequest);

        return ResponseDTO.creatDTO();
    }

    @PostMapping("/isWowNowImC2g")
    public ResponseDTO<?> isWowNowIMForC2g(@RequestBody ChatMemberList chatMemberList) {

        boolean isWowNowIM = true;

        for (String member : chatMemberList.getMemberList()) {
            UserDeviceInfoGetReqDTO reqDTO = new UserDeviceInfoGetReqDTO();
            reqDTO.setOperatorNo(member);
            UserAppVersionDO userAppVersionDO = userAppVersionService.getByOperatorNo(member);

            String appVersion = null;
            String appId = null;
            String deviceType = null;
            if (userAppVersionDO == null) {
                UserDeviceForImRespDTO userDeviceInfoRespDTO = userOperatorFacade.getDeviceInfoForIm(reqDTO);
                if (userDeviceInfoRespDTO == null) {
                    continue;
                }else{
                    appVersion = userDeviceInfoRespDTO.getAppVersion();
                    appId = userDeviceInfoRespDTO.getAppId();
                    deviceType = userDeviceInfoRespDTO.getDeviceType();
                }

            }else {
                appVersion = userAppVersionDO.getAppVersion();
                appId = userAppVersionDO.getAppId();
                deviceType = userAppVersionDO.getDeviceType();
            }

            if (DEVICE_TYPE_ANDROID.equals(deviceType.toUpperCase())
                    && APP_ID_DRIVER.equals(appId)) {
                if (VersionComparator.INSTANCE.compare(wownowImDriverAndroidVersion, appVersion) > 0) {
                    isWowNowIM = false;
                    break;
                }
            }

            if (DEVICE_TYPE_IOS.equals(deviceType.toUpperCase())
                    && APP_ID_DRIVER.equals(appId)) {
                if (VersionComparator.INSTANCE.compare(wownowImDriverIosVersion, appVersion) > 0) {
                    isWowNowIM = false;
                    break;
                }
            }


            if(DEVICE_TYPE_ANDROID.equals(deviceType.toUpperCase())
                && APP_ID_SUPPER.equals(appId)) {
                if (VersionComparator.INSTANCE.compare(wownowImC2gSuperAppAndroidVersion, appVersion) > 0) {
                    isWowNowIM = false;
                    break;
                }
            }

            if(DEVICE_TYPE_IOS.equals(deviceType.toUpperCase())
                    && APP_ID_SUPPER.equals(appId)) {
                if (VersionComparator.INSTANCE.compare(wownowImC2gSuperAppIOSVersion, appVersion) > 0) {
                    isWowNowIM = false;
                    break;
                }
            }

            if(DEVICE_TYPE_IOS.equals(deviceType.toUpperCase())&& APP_ID_Delivery.equals(appId)){
                if (VersionComparator.INSTANCE.compare(wownowImDeliveryIosVersion, appVersion) > 0) {
                    isWowNowIM = false;
                    break;
                }
            }

            if(DEVICE_TYPE_ANDROID.equals(deviceType.toUpperCase())&& APP_ID_Delivery.equals(appId)){
                if (VersionComparator.INSTANCE.compare(wownowImDeliveryAndroidVersion, appVersion) > 0) {
                    isWowNowIM = false;
                    break;
                }
            }
        }

        return ResponseDTO.creatDTO(isWowNowIM);

    }

    @PostMapping("/isWowNowIM")
    public ResponseDTO<?> isWowNowIM(@RequestBody ChatMemberList chatMemberList) {

        boolean isWowNowIM = true;

        for (String member : chatMemberList.getMemberList()) {
            UserDeviceInfoGetReqDTO reqDTO = new UserDeviceInfoGetReqDTO();
            reqDTO.setOperatorNo(member);
            UserAppVersionDO userAppVersionDO = userAppVersionService.getByOperatorNo(member);

            String appVersion = null;
            String appId = null;
            String deviceType = null;
            if (userAppVersionDO == null) {
                UserDeviceForImRespDTO userDeviceInfoRespDTO = userOperatorFacade.getDeviceInfoForIm(reqDTO);
                if (userDeviceInfoRespDTO == null) {
                    continue;
                }else{
                    appVersion = userDeviceInfoRespDTO.getAppVersion();
                    appId = userDeviceInfoRespDTO.getAppId();
                    deviceType = userDeviceInfoRespDTO.getDeviceType();
                }

            }else {
                appVersion = userAppVersionDO.getAppVersion();
                appId = userAppVersionDO.getAppId();
                deviceType = userAppVersionDO.getDeviceType();
            }

            if ("ANDROID".equals(deviceType.toUpperCase())
                    && "GoNowDriver".equals(appId)) {
                if (VersionComparator.INSTANCE.compare(wownowImDriverAndroidVersion, appVersion) > 0) {
                    isWowNowIM = false;
                    break;
                }
            }

            if ("IOS".equals(deviceType.toUpperCase())
                    && "GoNowDriver".equals(appId)) {
                if (VersionComparator.INSTANCE.compare(wownowImDriverIosVersion, appVersion) > 0) {
                    isWowNowIM = false;
                    break;
                }
            }


            if("ANDROID".equals(deviceType.toUpperCase())
                && "SuperApp".equals(appId)) {
                if (VersionComparator.INSTANCE.compare(wownowImSuperAppAndroidVersion, appVersion) > 0) {
                    isWowNowIM = false;
                    break;
                }
            }

            if("IOS".equals(deviceType.toUpperCase())
                    && "SuperApp".equals(appId)) {
                if (VersionComparator.INSTANCE.compare(wownowImSuperAppIOSVersion, appVersion) > 0) {
                    isWowNowIM = false;
                    break;
                }
            }

            if("IOS".equals(deviceType.toUpperCase())&& "Delivery".equals(appId)){
                if (VersionComparator.INSTANCE.compare(wownowImDeliveryIosVersion, appVersion) > 0) {
                    isWowNowIM = false;
                    break;
                }
            }

            if("ANDROID".equals(deviceType.toUpperCase())&& "Delivery".equals(appId)){
                if (VersionComparator.INSTANCE.compare(wownowImDeliveryAndroidVersion, appVersion) > 0) {
                    isWowNowIM = false;
                    break;
                }
            }
        }

        return ResponseDTO.creatDTO(isWowNowIM);

    }
}
