package com.chaos.im.chat;

import com.alibaba.dubbo.config.spring.context.annotation.EnableDubbo;
import com.chaos.keep.alive.common.web.config.FastJsonConfig;
import com.chaos.keep.alive.common.web.config.GlobalExceptionConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication
@EnableTransactionManagement(proxyTargetClass = true)
@Import({FastJsonConfig.class, GlobalExceptionConfig.class})
@EnableDubbo
@ComponentScan(basePackages = {
        "com.chaos.im.chat",
        "com.chaos.im.c2c",
        "com.chaos.im.c2g",
        "com.chaos.keep.alive.common" // 其他需要扫描的包

})
public class ChatApplication {

    public static void main(String[] args) {
        SpringApplication.run(ChatApplication.class, args);
    }
}
