package com.chaos.im.chat.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chaos.keep.alive.common.persistent.domain.BaseDO;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("chat_member")
public class ChatMemberDO extends BaseDO {

    private Long chatId;

    private Integer type;

    private String peerId;

    private String memberId;

    private String nickname;

    private String avatar;

    private LocalDateTime activeTime;

    private String ex;
}
