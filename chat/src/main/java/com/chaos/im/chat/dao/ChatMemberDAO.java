package com.chaos.im.chat.dao;


import com.chaos.im.chat.domain.ChatMemberDO;
import com.chaos.keep.alive.common.persistent.dao.BaseDAO;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface ChatMemberDAO extends BaseDAO<ChatMemberDO> {

    List<ChatMemberDO> list(String memberId);

    Optional<ChatMemberDO> get(Integer type, String memberId, String peerId);

    Optional<ChatMemberDO> get(Integer type, String peerId);

    Optional<ChatMemberDO> getOneByChatId(Long chatId);
}
