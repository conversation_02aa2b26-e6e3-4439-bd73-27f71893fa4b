package com.chaos.im.chat.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaos.im.chat.domain.ChatMemberDO;

import java.time.LocalDateTime;
import java.util.List;

public interface ChatMemberService {

    String getPeerIdByChatId(Long chatId);

    int countByChatId(Long chatId);

    void deleteByChatId(Long chatId);

    void deleteChatMember(Integer type, Long groupId);

    void deleteChatMember(Integer type, Long chatId, String memberId);

    void updateActiveTimeByChatId(Long chatId, String operatorNo, LocalDateTime activeTime);

    IPage<ChatMemberDO> pageByMemberId(String memberId, Integer current, Integer pageSize);

    Long getChatIdByGroupId(Long groupId);

    List<ChatMemberDO> listByChatIds(List<Long> chatIds,String memberId);

    ChatMemberDO getByChatIdAndMemberId(Long chatId,String memberId);
    ChatMemberDO getOneByChatId(Long chatId);

    ChatMemberDO getByPeerIdAndMemberId(String peerId,Integer type,String memberId);
}
