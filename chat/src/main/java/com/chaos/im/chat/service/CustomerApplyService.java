package com.chaos.im.chat.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chaos.im.chat.domain.AcceptApplyReq;
import com.chaos.im.chat.domain.CustomerApplyDO;
import com.chaos.im.chat.domain.PageCustomerApplyReq;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 客户申请服务接口
 * 
 * <AUTHOR>
 */
public interface CustomerApplyService extends IService<CustomerApplyDO> {

    /**
     * 分页查询客户申请
     * 
     * @param page 分页参数
     * @param customerApply 查询条件
     * @return 分页结果
     */
    IPage<CustomerApplyDO> page(Page<CustomerApplyDO> page, PageCustomerApplyReq customerApply);

    /**
     * 根据ID查询客户申请
     * 
     * @param id 主键ID
     * @return 客户申请信息
     */
    CustomerApplyDO getById(Long id);

    /**
     * 根据订单号查询客户申请
     *
     * @param orderNo 订单号
     * @return 客户申请信息
     */
    List<CustomerApplyDO> getByOrderNo(String orderNo);

    /**
     * 根据聊天ID查询客户申请列表
     * 
     * @param chatId 聊天ID
     * @return 客户申请列表
     */
    List<CustomerApplyDO> listByChatId(Long chatId);


    /**
     * 根据用户手机号查询客户申请列表
     *
     * @param userMobile 用户手机号
     * @return 客户申请列表
     */
    List<CustomerApplyDO> listByUserMobile(String userMobile);

    /**
     * 根据用户名查询客户申请列表
     *
     * @param userName 用户名
     * @return 客户申请列表
     */
    List<CustomerApplyDO> listByUserName(String userName);

    /**
     * 根据时间范围查询客户申请列表
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 客户申请列表
     */
    List<CustomerApplyDO> listByTimeRange(Date startTime, Date endTime);

    /**
     * 保存客户申请
     * 
     * @param customerApply 客户申请信息
     * @return 是否成功
     */
    @Transactional
    boolean save(CustomerApplyDO customerApply);

    /**
     * 更新客户申请
     * 
     * @param customerApply 客户申请信息
     * @return 是否成功
     */
    @Transactional
    boolean updateById(CustomerApplyDO customerApply);


    @Transactional
    boolean updateReadStatus(String orderNo, Integer readStatus);


    @Transactional
    boolean updateHandleStatus(String orderNo, Integer handleStatus);

    /**
     * 根据ID删除客户申请
     * 
     * @param id 主键ID
     * @return 是否成功
     */
    @Transactional
    boolean removeById(Long id);

    /**
     * 批量删除客户申请
     * 
     * @param ids 主键ID列表
     * @return 是否成功
     */
    @Transactional
    boolean removeByIds(List<Long> ids);

    void acceptCustomerApply(AcceptApplyReq reqDTO);


    CustomerApplyDO selectByOrderNoAndType(String orderNo, Integer type, String userOperatorNo);
}
