package com.chaos.im.chat.service;

import com.chaos.im.chat.domain.ReportAppVersionRequest;
import com.chaos.im.chat.domain.UserAppVersionDO;

public interface UserAppVersionService {
    void save(ReportAppVersionRequest request);

    UserAppVersionDO getByAppIdOpDeviceIdLoginName(String appId, String operatorNo, String deviceId, String loginName);

    UserAppVersionDO getByOperatorNo(String operatorNo);
}
