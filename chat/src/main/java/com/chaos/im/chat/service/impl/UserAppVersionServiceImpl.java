package com.chaos.im.chat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaos.im.chat.domain.ReportAppVersionRequest;
import com.chaos.im.chat.domain.UserAppVersionDO;
import com.chaos.im.chat.mapper.UserAppVersionMapper;
import com.chaos.im.chat.service.UserAppVersionService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class UserAppVersionServiceImpl extends ServiceImpl<UserAppVersionMapper, UserAppVersionDO> implements UserAppVersionService {

    @Override
    public void save(ReportAppVersionRequest request) {
       UserAppVersionDO exist = getByAppIdOpDeviceIdLoginName(request.getAppId(),request.getOperatorNo(),request.getDeviceId(),request.getLoginName());
        if (exist != null) {
            exist.setAppVersion(request.getAppVersion());
            exist.setGmtModified(LocalDateTime.now());
            saveOrUpdate(exist);
        }else {
            UserAppVersionDO userAppVersionDO = new UserAppVersionDO();
            userAppVersionDO.setAppVersion(request.getAppVersion());
            userAppVersionDO.setAppId(request.getAppId());
            userAppVersionDO.setDeviceId(request.getDeviceId());
            userAppVersionDO.setDeviceInfo(request.getDeviceInfo());
            userAppVersionDO.setOperatorNo(request.getOperatorNo());
            userAppVersionDO.setLoginName(request.getLoginName());
            userAppVersionDO.setGmtCreate(LocalDateTime.now());
            userAppVersionDO.setGmtModified(LocalDateTime.now());
            userAppVersionDO.setDeviceType(request.getDeviceType());
            save(userAppVersionDO);
        }
    }

    @Override
    public UserAppVersionDO getByAppIdOpDeviceIdLoginName(String appId, String operatorNo, String deviceId, String loginName){
        QueryWrapper<UserAppVersionDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("app_id", appId);
        queryWrapper.eq("operator_no", operatorNo);
        queryWrapper.eq("device_id", deviceId);
        queryWrapper.eq("login_name", loginName);

        queryWrapper.orderByDesc("gmt_create");


        return getOne(queryWrapper);
    }

    @Override
    public UserAppVersionDO getByOperatorNo(String operatorNo){
        QueryWrapper<UserAppVersionDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operator_no", operatorNo);
        queryWrapper.orderByDesc("gmt_create");
        return getOne(queryWrapper,false);
    }
}
