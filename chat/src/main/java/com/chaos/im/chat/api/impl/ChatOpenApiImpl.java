package com.chaos.im.chat.api.impl;

import com.chaos.im.chat.api.ChatOpenApi;
import com.chaos.im.chat.domain.*;
import com.chaos.im.chat.service.ChatOpenApiService;
import com.outstanding.framework.core.ResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

@Slf4j
public class ChatOpenApiImpl implements ChatOpenApi {

    @Autowired
    private ChatOpenApiService chatOpenApiService;

    @Override
    public ResponseDTO<?> page(PageCustomerApplyReq customerApply) {
       return chatOpenApiService.page(customerApply);
    }

    @Override
    public ResponseDTO<List<CustomerChatVO>> getByOrderNo(GetChatByOrderNoReq req) {
        return chatOpenApiService.getByOrderNo(req);
    }

    @Override
    public ResponseDTO<Boolean> updateHandleStatus(UpdateOrderApplyStatusReq req) {
       return chatOpenApiService.updateHandleStatus(req);
    }

    @Override
    public ResponseDTO<?> updateReadStatus(UpdateOrderApplyStatusReq req) {
       return chatOpenApiService.updateReadStatus(req);
    }

    @Override
    public ResponseDTO<?> acceptCustomerApply(AcceptApplyReq reqDTO) {
        return chatOpenApiService.acceptCustomerApply(reqDTO);
    }

    @Override
    public ResponseDTO<List<ApplyCustomersRefDTO>> listCustomerByApplyId(CustomerListReq req){
        return chatOpenApiService.listCustomerByApplyId(req);
    }
}
