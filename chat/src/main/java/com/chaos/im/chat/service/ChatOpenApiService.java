package com.chaos.im.chat.service;

import com.chaos.im.chat.domain.*;
import com.outstanding.framework.core.ResponseDTO;

import java.util.List;

public interface ChatOpenApiService {
    ResponseDTO<?> page(PageCustomerApplyReq customerApply);

    ResponseDTO<List<CustomerChatVO>> getByOrderNo(GetChatByOrderNoReq req);

    ResponseDTO<Boolean> updateHandleStatus(UpdateOrderApplyStatusReq req);

    ResponseDTO<?> updateReadStatus(UpdateOrderApplyStatusReq req);

    ResponseDTO<?> acceptCustomerApply(AcceptApplyReq reqDTO);

    ResponseDTO<List<ApplyCustomersRefDTO>> listCustomerByApplyId(CustomerListReq req);

    boolean isExistCustomerApply(IsExistCustomerApplyReq req);
}
