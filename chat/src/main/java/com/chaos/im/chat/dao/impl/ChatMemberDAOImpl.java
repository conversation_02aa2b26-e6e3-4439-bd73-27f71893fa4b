package com.chaos.im.chat.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chaos.im.chat.dao.ChatMemberDAO;
import com.chaos.im.chat.domain.ChatMemberDO;
import com.chaos.im.chat.mapper.ChatMemberMapper;
import com.chaos.keep.alive.common.web.dao.MybatisPlusDAOImpl;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Repository
public class ChatMemberDAOImpl extends MybatisPlusDAOImpl<ChatMemberMapper, ChatMemberDO> implements ChatMemberDAO {

    @Override
    public List<ChatMemberDO> list(String memberId) {
        LambdaQueryWrapper<ChatMemberDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatMemberDO::getMemberId, memberId).orderByDesc(ChatMemberDO::getActiveTime);
        return mapper.selectList(queryWrapper);
    }

    @Override
    public Optional<ChatMemberDO> get(Integer chatType, String memberId, String peerId) {
        LambdaQueryWrapper<ChatMemberDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatMemberDO::getMemberId, memberId);
        queryWrapper.eq(ChatMemberDO::getPeerId,peerId );
        queryWrapper.eq(ChatMemberDO::getType, chatType);
        return Optional.ofNullable(mapper.selectOne(queryWrapper));
    }

    @Override
    public Optional<ChatMemberDO> get(Integer chatType, String peerId) {
        LambdaQueryWrapper<ChatMemberDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatMemberDO::getPeerId, peerId);
        List<ChatMemberDO> chatMemberDOList =  mapper.selectList(queryWrapper);
        if (!chatMemberDOList.isEmpty()){
            return Optional.ofNullable(chatMemberDOList.get(0));
        }else{
            return Optional.empty();
        }
    }

    @Override
    public Optional<ChatMemberDO> getOneByChatId(Long chatId){
        LambdaQueryWrapper<ChatMemberDO> queryWrapper =  new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatMemberDO::getChatId, chatId);
        List<ChatMemberDO> chatMemberDOList =  mapper.selectList(queryWrapper);
        if(!chatMemberDOList.isEmpty()) {
            return Optional.ofNullable(chatMemberDOList.get(0));
        }else{
            return Optional.empty();
        }
    }
}