package com.chaos.im.chat.controller;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaos.im.c2g.group.api.GroupApi;
import com.chaos.im.c2g.group.api.domain.GroupVO;
import com.chaos.im.chat.domain.*;
import com.chaos.im.chat.service.ChatMemberService;
import com.chaos.im.chat.service.ChatService;
import com.chaos.keep.alive.common.core.exception.BusinessException;
import com.chaos.keep.alive.common.core.util.BeanCopierUtils;
import com.chaos.keep.alive.common.web.util.PageUtil;
import com.outstanding.framework.core.ResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/chat")
@Slf4j
public class ChatController {

    @Autowired
    private ChatService chatService;

    @Autowired
    private ChatMemberService chatMemberService;

    @Autowired
    private GroupApi groupApi;

    @PostMapping("/list")
    ResponseDTO<?> list(@RequestBody ChatListRequest chatListRequest, @RequestHeader("deviceId") String deviceId) {

        if (chatListRequest.getPageSize() > 20) {
            throw new BusinessException("pageSize不得大于20");
        }

        chatListRequest.setDeviceId(deviceId);

        IPage<ChatMemberDTO> page = null;
        try {
            page = chatService.pageChat(chatListRequest);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        List<ChatMemberVO> chatMemberVOList = page.getRecords().stream().map(it -> {
            ChatMemberVO vo = new ChatMemberVO();
            BeanUtils.copyProperties(it, vo);
            if (ObjectUtil.isNotNull(it.getLastMessage())) {
                vo.setActiveTime(it.getLastMessage().getTimestamp());
            }
            if (ObjectUtil.isNotNull(it.getLastMessage())) {
                LastMessageVO lastMessageDTO = new LastMessageVO();
                BeanUtils.copyProperties(it.getLastMessage(), lastMessageDTO);
                vo.setLastMessage(lastMessageDTO);
            }
            if (ObjectUtil.isNotNull(it.getLastMessage()) && ObjectUtil.isNotNull(it.getLastMessage().getUnReadCount())) {
                vo.setUnReadCount(it.getLastMessage().getUnReadCount());
            }
            return vo;
        }).collect(Collectors.toList());

        return ResponseDTO.creatDTO(PageUtil.createPage(page.getCurrent(), page.getSize(), page.getTotal(), chatMemberVOList));

    }

    @PostMapping("/create")
    ResponseDTO<?> add(@RequestBody CreateChatRequest createChatRequest, @RequestHeader("deviceId") String deviceId) {
        createChatRequest.setDeviceId(deviceId);
        log.info("create chat request:{}", createChatRequest);
        return ResponseDTO.creatDTO(chatService.createChat(createChatRequest));
    }

    @PostMapping("/createGroupChat")
    ResponseDTO<?> createGroupChat(@RequestBody CreateGroupChatRequest createChatRequest, @RequestHeader("deviceId") String deviceId) {
        createChatRequest.setDeviceId(deviceId);
        log.info("create chat request:{}", JSONUtil.toJsonStr(createChatRequest));
        return ResponseDTO.creatDTO(chatService.createGroupChat(createChatRequest));
    }

    @PutMapping("/updateActiveTime")
    ResponseDTO<?> updateActiveTime(Long chatId, String operatorNo, @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime activeTime) {
        chatMemberService.updateActiveTimeByChatId(chatId, operatorNo, activeTime);
        return ResponseDTO.creatDTO();
    }


    @DeleteMapping("/delete")
    ResponseDTO<?> delete(Long chatId, String operatorNo) {
        chatService.delete(chatId, operatorNo);
        return ResponseDTO.creatDTO();
    }

    @PostMapping("/queryByOrderNo")
    ResponseDTO<?> queryByOrderNo(@RequestBody QueryByOrderNoReq req) {
        GroupVO groupVO = groupApi.queryByOrderNo(req.getOperatorNo(), req.getOrderNo(), req.getBizType());
        if(groupVO == null){
            return ResponseDTO.creatDTO();
        }
        ChatMemberDO chatMemberDO = chatMemberService.getByPeerIdAndMemberId(groupVO.getId().toString(),2,req.getOperatorNo());
        ChatMemberDTO chatMemberDTO = new ChatMemberDTO();
        BeanCopierUtils.copy(chatMemberDO, chatMemberDTO);
        return ResponseDTO.creatDTO(chatMemberDTO);
    }
}
