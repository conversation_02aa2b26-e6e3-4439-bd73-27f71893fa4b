package com.chaos.im.chat.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chaos.im.c2g.group.api.GroupApi;
import com.chaos.im.chat.api.ChatApi;
import com.chaos.im.chat.api.ChatOpenApi;
import com.chaos.im.chat.domain.*;
import com.chaos.im.chat.service.ChatMemberService;
import com.chaos.im.chat.service.ChatOpenApiService;
import com.chaos.im.chat.service.CustomerApplyService;
import com.chaos.keep.alive.common.core.exception.BusinessException;
import com.chaos.keep.alive.common.im.constant.Constants;
import com.chaos.keep.alive.common.im.constant.ImConstants;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.core.ResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * 客户申请控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/customerApply")
@Slf4j
public class CustomerApplyController {

    @Autowired
    private CustomerApplyService customerApplyService;

    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private ChatMemberService chatMemberService;

    @DubboReference(version = "1.0.0")
    private GroupApi groupApi;
    @Autowired
    private ChatApi chatApi;

    @Autowired
    private ChatOpenApiService chatOpenApiService;

    @PostMapping("/exist")
    public ResponseDTO<?> isExist(@RequestBody IsExistCustomerApplyReq req){
        return ResponseDTO.creatDTO(chatOpenApiService.isExistCustomerApply(req));
    }

    /**
     * 分页查询客户申请
     */
    @PostMapping("/page")
    public ResponseDTO<?> page(@RequestBody PageCustomerApplyReq customerApply) {
       return chatOpenApiService.page(customerApply);
    }

    /**
     * 根据订单号查询客户申请
     */
    @PostMapping("/order/chat")
    public ResponseDTO<?> getByOrderNo(@RequestBody GetChatByOrderNoReq req) {
        return chatOpenApiService.getByOrderNo(req);

    }

    /**
     * 新增客户申请
     */
    @PostMapping("/create")
    public ResponseDTO<Boolean> save(@RequestBody @Validated CreateCustomerApplyReq createCustomerApplyReq) {
        if (ObjectUtil.isNull(createCustomerApplyReq)) {
            throw new BusinessException("客户申请信息不能为空");
        }

        if(createCustomerApplyReq.getType()==2 &&
                (createCustomerApplyReq.getRiderNo()==null || createCustomerApplyReq.getRiderName()==null
                        || createCustomerApplyReq.getRiderHeadUrl()==null)){
            throw new BusinessException("type=2时,riderNo、riderName、riderHeadUrl不许为空");
        }

        if(createCustomerApplyReq.getType()==3 && createCustomerApplyReq.getChatId()==null){
            throw new BusinessException("type=3时，必须传入chatId");
        }

        CustomerApplyDO customerApplyDO = new CustomerApplyDO();
        BeanUtils.copyProperties(createCustomerApplyReq, customerApplyDO);
        customerApplyDO.setRiderOperatorNo(createCustomerApplyReq.getRiderNo());
        boolean result = customerApplyService.save(customerApplyDO);

        return ResponseDTO.creatDTO(result);
    }



    /**
     * 更新读取状态
     */
    @PostMapping("/updateReadStatus")
    public ResponseDTO<?> updateReadStatus(@RequestBody UpdateOrderApplyStatusReq updateOrderApplyReadStatusReq) {
       return chatOpenApiService.updateReadStatus(updateOrderApplyReadStatusReq);
    }

    @PostMapping("/handlerStatus")
    public ResponseDTO<?> updateHandlerStatus(@RequestBody UpdateOrderApplyStatusReq req) {
        return chatOpenApiService.updateHandleStatus(req);
    }


    @PostMapping("/accept")
    public ResponseDTO<?> acceptCustomerApply(@RequestBody AcceptApplyReq reqDTO) {
       return chatOpenApiService.acceptCustomerApply(reqDTO);
    }

    @PostMapping("/customerList")
    public ResponseDTO<?> listCustomerByApplyId(@RequestBody CustomerListReq req){
        return chatOpenApiService.listCustomerByApplyId(req);
    }
}
