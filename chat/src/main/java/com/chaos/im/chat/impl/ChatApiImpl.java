package com.chaos.im.chat.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaos.im.chat.api.ChatApi;
import com.chaos.im.chat.domain.ChatDTO;
import com.chaos.im.chat.domain.ChatMemberDO;
import com.chaos.im.chat.domain.ChatMemberApiVO;
import com.chaos.im.chat.service.ChatMemberService;
import com.chaos.im.chat.service.ChatService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class ChatApiImpl implements Chat<PERSON>pi {

    @Autowired
    private ChatService chatService;

    @Autowired
    private ChatMemberService chatMemberService;

    @Override
    public void addChat(ChatDTO chatDTO) {
        chatService.save(chatDTO,null);
    }

    @Override
    public String getPeerIdByChatId(Long chatId) {
       return  chatMemberService.getPeerIdByChatId(chatId);
    }

    @Override
    public void deleteChatMember(Integer type,Long chatId, String memberId) {
        chatMemberService.deleteChatMember(type,chatId,memberId);
    }

    @Override
    public void deleteChatByGroupId(Integer type, Long groupId) {
        Long chatId = chatService.getChatIdByGroupId(groupId);
        chatService.deleteByChatId(chatId);
    }

    @Override
    public void deleteChatByGroupId(Integer type, Long groupId, String memberId) {
        Long chatId = chatService.getChatIdByGroupId(groupId);
        chatMemberService.deleteChatMember(type,chatId,memberId);
    }

    @Override
    public Long getChatIdByGroupId(Long groupId) {
       return chatService.getChatIdByGroupId(groupId);
    }

    @Override
    public List<ChatMemberApiVO> getChatMemberByOperatorNo(String operatorNo) {

        IPage<ChatMemberDO> page  = chatMemberService.pageByMemberId(operatorNo,1,100);

        return page.getRecords().stream().map(chatMemberDO -> {
            ChatMemberApiVO chatMemberApiVO = new ChatMemberApiVO();
            BeanUtils.copyProperties(chatMemberDO,chatMemberApiVO);
            return chatMemberApiVO;
        }).collect(Collectors.toList());

    }

}
