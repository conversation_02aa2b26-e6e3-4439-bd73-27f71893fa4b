create table customer_apply
(
    id                bigint auto_increment
        primary key,
    order_no          varchar(256)  null,
    order_time        datetime      null,
    user_operator_no  varchar(1024) null,
    user_name         varchar(256)  null,
    user_mobile       varchar(256)  null,
    receiver_name     varchar(128)  null comment '收货人姓名',
    receiver_mobile   varchar(128)  null comment '收货人手机',
    head_url          varchar(1024) null,
    rider_operator_no varchar(256)  null,
    rider_mobile      varchar(30)   null,
    rider_name        varchar(256)  null,
    rider_head_url    varchar(1024) null,
    type              int           null comment '1:用户与客服。2:骑手与客服。3:用户骑手客服',
    lang              varchar(256)  null comment '语言',
    read_status       int default 0 null,
    intervene_status  int default 0 null,
    handle_status     int default 0 null,
    apply_time        datetime      null,
    chat_id           bigint        null,
    merchant_no       varchar(256)  null,
    merchant_name     varchar(256)  null,
    merchant_mobile   varchar(256)  null,
    merchant_head_url varchar(1024) null,
    gmt_create        datetime      null,
    gmt_modified      datetime      null
)
    comment '申请客服表';

create table apply_customers_ref
(
    id           int auto_increment
        primary key,
    apply_id     bigint                             null,
    customer_no  varchar(128)                       null,
    gmt_create   datetime default (now())           null,
    gmt_modified datetime default CURRENT_TIMESTAMP null
);



alter table chat_member
    add ex varchar(2048) null;