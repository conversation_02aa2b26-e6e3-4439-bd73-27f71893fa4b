package com.chaos.keep.alive.client.mock.http;

import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.chaos.keep.alive.client.config.PropertiesUtils;
import com.chaos.keep.alive.common.core.domain.JsonResult;
import lombok.SneakyThrows;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class AuthRemote {

    private final static String authUrl = PropertiesUtils.getWebGatewayUrl() + "/api/auth/token";

    @SneakyThrows
    public JsonResult<?> login(String mobile) {
        Map<String, Object> params = new HashMap<>();
        params.put("grantType", "mobile");
        params.put("mobile", mobile);
        String jsonStr = HttpRequest.post(authUrl)
                .basicAuth("account", "vXvLIQjvVoqWxbcOV6oyhjZaU89wHMby7H3Hp6iHNkaU6ODF2PasvSZz2MA8aL4j")
                .form(params)
                .execute().body();
        return JSONUtil.toBean(jsonStr, JsonResult.class);
    }
}
