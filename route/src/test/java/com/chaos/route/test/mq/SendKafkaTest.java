package com.chaos.route.test.mq;

import cn.hutool.json.JSONUtil;
import com.chaos.keep.alive.common.core.constant.CommandType;
import com.chaos.keep.alive.common.core.constant.MessageType;
import com.chaos.keep.alive.common.core.domain.MessageJsonPush;
import com.chaos.route.domain.OrderStatusChangeMessage;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.UUID;

@RunWith(SpringRunner.class)
@SpringBootTest
public class SendKafkaTest {

    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;


    @Test
    public void sendOrderStatusChangePush(){

        MessageJsonPush messageJsonPush = new MessageJsonPush();
        messageJsonPush.setToOperatorNo("8618950125817");
        messageJsonPush.setBizType(CommandType.COMMAND_GONOW_ORDER_STATUS_CHANGE);
        messageJsonPush.setMessageType(MessageType.MESSAGE_TYPE_PUSH);
        messageJsonPush.setMessageId(UUID.randomUUID().toString().replace("-",""));

        OrderStatusChangeMessage orderStatusChangeMessage = new OrderStatusChangeMessage();
        orderStatusChangeMessage.setBeforeStatus(1);
        orderStatusChangeMessage.setChangedStatus(2);
        orderStatusChangeMessage.setOrderId("123456789");
        messageJsonPush.setContent(JSONUtil.toJsonStr(orderStatusChangeMessage));
        messageJsonPush.setToAppId("GoNowDriver");

        kafkaTemplate.send("topic-gonow-order-status-change-local", "1", JSONUtil.toJsonStr(messageJsonPush));
    }
}
