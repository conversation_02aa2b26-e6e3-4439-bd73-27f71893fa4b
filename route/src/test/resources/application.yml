server:
  port: 9097
spring:
  profiles:
    active: local
  main:
    banner-mode: "off"
    allow-bean-definition-overriding: true
  application:
    name: gonow-ka-route
  redis:
    cluster:
      nodes: drc-redis-0-0.redis-svc-0.lifekh-tool-sit.svc.cluster.local:6379,drc-redis-0-1.redis-svc-0.lifekh-tool-sit.svc.cluster.local:6379,drc-redis-1-0.redis-svc-1.lifekh-tool-sit.svc.cluster.local:6379,drc-redis-1-1.redis-svc-1.lifekh-tool-sit.svc.cluster.local:6379,drc-redis-2-0.redis-svc-2.lifekh-tool-sit.svc.cluster.local:6379,drc-redis-2-1.redis-svc-2.lifekh-tool-sit.svc.cluster.local:6379

  kafka:
    producer:
      bootstrap-servers: 172.16.27.12:29092
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      bootstrap-servers: 172.16.27.12:29092
      group-id: ka-route
      auto-offset-reset: latest
      enable-auto-commit: false
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    listener:
      type: batch
      ack-mode: manual_immediate
ka:
  serverId: keep-alive-route
  port: 9670
  authenticate: false
  heartbeat:
    readTimeout: 30000
  zk:
    enable: true
    retry: 3
    intervalTime: 1000
    zkServer: localhost:2181
dubbo:
  application:
    name: driver-trajectory
  registry:
    address: zookeeper://zk-dubbo-0.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181?backup=zk-dubbo-1.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181,zk-dubbo-2.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181
    #address: zookeeper://zk-dubbo-0.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181?backup=zk-dubbo-1.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181,zk-dubbo-2.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181
  protocol:
    port: 20990
  provider:
    group: chaos
  consumer:
    group: chaos