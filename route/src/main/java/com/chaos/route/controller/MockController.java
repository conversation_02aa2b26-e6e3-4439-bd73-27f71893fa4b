package com.chaos.route.controller;

import cn.hutool.json.JSONUtil;
import com.chaos.keep.alive.common.core.constant.CommandType;
import com.chaos.keep.alive.common.core.domain.ReportLocationJsonRequest;
import com.chaos.route.domain.MockDriverLocationReportReq;
import com.outstanding.framework.core.ResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.protocol.ResponseCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/mock")
@Slf4j
public class MockController {

//    @Value("#{'topic-gonow-driver-report-location-qa'.concat('${spring.profiles.active}')}")
//    private String TOPIC_DRIVER_REPORT_LOCATION;

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    private SynchronousQueue queue = new SynchronousQueue<>();

    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(
            5, 20, 10, TimeUnit.SECONDS,
            queue, new ThreadPoolExecutor.CallerRunsPolicy());


    @PostMapping("/mockDriverLocationReport")
    public ResponseDTO<?> mockDriverLocationReport(@RequestBody MockDriverLocationReportReq req) {

        ReportLocationJsonRequest request = new ReportLocationJsonRequest();
        request.setAppId(req.getAppId());
        request.setTimestamp(System.currentTimeMillis());
        request.setDeviceId(req.getDeviceId());
        request.setMessageId(UUID.randomUUID().toString().replace("-", ""));
        request.setDriverId(req.getDriverId());
        request.setDirection(req.getDirection());
        request.setSpeed(req.getSpeed());
        request.setLongitude(req.getLongitude());
        request.setLatitude(req.getLatitude());
        request.setOrderId(req.getOrderId());
        kafkaTemplate.send("topic-gonow-driver-report-location-qa", String.valueOf(request.getDriverId()), JSONUtil.toJsonStr(request));
        log.info("mock上报位置数据到kafka:{}", JSONUtil.toJsonStr(request));

        log.info("发送kafka消息完成。。。。。。");

        return ResponseDTO.creatDTO(ResponseCode.SUCCESS);
    }

    @PostMapping("/mockDriverLocationReportBatch")
    public ResponseDTO<?> mockDriverLocationReportBatch(@RequestBody MockDriverLocationReportReq req) {

        int num = req.getInsertNum() == null ? 60000 : req.getInsertNum();
        for (int i = 0; i < num; i++) {

            executor.execute(() -> {
                ReportLocationJsonRequest request = new ReportLocationJsonRequest();
                request.setAppId(req.getAppId());
                request.setTimestamp(System.currentTimeMillis());
                request.setDeviceId(req.getDeviceId());
                request.setMessageId(UUID.randomUUID().toString().replace("-", ""));
                request.setDriverId(req.getDriverId());
                request.setDirection(req.getDirection());
                request.setSpeed(req.getSpeed());
                request.setLongitude(req.getLongitude());
                request.setLatitude(req.getLatitude());
                request.setOrderId(req.getOrderId());
                kafkaTemplate.send("topic-gonow-driver-report-location-qa", String.valueOf(request.getDriverId()), JSONUtil.toJsonStr(request));
                log.info("mock上报位置数据到kafka:{}", JSONUtil.toJsonStr(request));
            });
            log.info("发送kafka消息完成。。。。。。");
        }

        return ResponseDTO.creatDTO(ResponseCode.SUCCESS);
    }

}
