package com.chaos.route.server.command;

import cn.hutool.json.JSONUtil;
import com.chaos.keep.alive.common.im.constant.CommandType;
import com.chaos.keep.alive.common.im.constant.Constants;
import com.chaos.keep.alive.common.im.constant.MessageType;
import com.chaos.keep.alive.common.im.domain.MessageAckJsonRequest;
import com.chaos.keep.alive.common.im.domain.MessageSendJsonRequest;
import com.chaos.keep.alive.common.protobuf.C2cMessageAckRequest;
import com.chaos.keep.alive.common.protobuf.C2cMessageSendRequest;
import com.chaos.keep.alive.common.protobuf.Command;
import com.chaos.route.util.RouteContext;
import io.netty.channel.ChannelHandlerContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class C2cMessageSendCommandHandler implements ServerCommandHandler {

    private final KafkaTemplate<String, String> kafkaTemplate;

    @Value("#{'topic-c2c-message-send-'.concat('${spring.profiles.active}')}")
    private String TOPIC_C2C_MESSAGE_SEND;


    @Value("#{'topic-c2c-message-ack-'.concat('${spring.profiles.active}')}")
    private String TOPIC_C2C_MESSAGE_ACK;

    private final Executor taskExecutor;

    private final RedisTemplate<String, Long> redisTemplate;

    private final RouteContext routeContext;

    @Override
    public void handleCommand(Command command, ChannelHandlerContext ctx) {
        taskExecutor.execute(() -> {
            try {
                if (command.getMessageType() ==  MessageType.MESSAGE_TYPE_CLIENT_ACK) {
                    C2cMessageAckRequest body = C2cMessageAckRequest.parseFrom(command.getBody());
                    MessageAckJsonRequest request = new MessageAckJsonRequest();
                    request.setDeviceId(body.getDeviceId());
                    request.setOperatorNo(body.getOperatorNo());
                    request.setMessageId(body.getMessageId());
                    request.setChatType(body.getChatType());
                    request.setChatId(body.getChatId());
                    request.setSequence(body.getSequence());

                    kafkaTemplate.send(TOPIC_C2C_MESSAGE_ACK, request.getOperatorNo(), JSONUtil.toJsonStr(request));
                    log.info("发送kafka消息:{}",JSONUtil.toJsonStr(request));

                }else {
                    C2cMessageSendRequest body = C2cMessageSendRequest.parseFrom(command.getBody());

                    MessageSendJsonRequest request = getMessageSendJsonRequest(body);
//                    Long chatId =  request.getChatId();
//                    Optional<Long> sequenceOptional = Optional.ofNullable(redisTemplate.boundValueOps(Constants.REDIS_SEQ_KEY + "::" +chatId).increment());
//                    // 服务端设置sequence
//                    request.setSequence(sequenceOptional.orElse(1L));
                    if (CommandType.COMMAND_IM_C2C_MESSAGE_SEND == command.getBizType()) {
                        kafkaTemplate.send(TOPIC_C2C_MESSAGE_SEND, request.getToOperatorNo(), JSONUtil.toJsonStr(request));

                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
    }

    private static MessageSendJsonRequest getMessageSendJsonRequest(C2cMessageSendRequest body) {
        MessageSendJsonRequest request = new MessageSendJsonRequest();
        request.setChatId(body.getChatId());
        request.setFromOperatorNo(body.getFromOperatorNo());
        request.setToOperatorNo(body.getToOperatorNo());
        request.setContent(body.getContent());
        request.setMessageId(body.getMessageId());
        request.setChatType(Constants.CHAT_TYPE_C2C);
        request.setCategory(body.getCategory());

        return request;
    }

    @Override
    public int getType() {
        return CommandType.COMMAND_IM_C2C_MESSAGE_SEND;
    }
}
