package com.chaos.route.server.command;

import com.chaos.keep.alive.common.core.constant.CommandType;
import com.chaos.keep.alive.common.protobuf.Command;
import com.chaos.keep.alive.common.protobuf.RegisterRequest;
import com.chaos.route.gateway.GatewayRegistry;
import io.netty.channel.ChannelHandlerContext;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RegisterServerCommandHandler implements ServerCommandHandler {

    private final GatewayRegistry gatewayRegistry;

    @SneakyThrows
    @Override
    public void handleCommand(Command command, ChannelHandlerContext ctx) {
        RegisterRequest body = RegisterRequest.parseFrom(command.getBody());
        String gatewayId = body.getServerId();
        log.info("接收到网关服务[{}]注册信息...", gatewayId);
        // 注册
        gatewayRegistry.register(gatewayId, ctx);
        ctx.writeAndFlush(command);
    }

    @Override
    public int getType() {
        return CommandType.COMMAND_REGISTER;
    }
}
