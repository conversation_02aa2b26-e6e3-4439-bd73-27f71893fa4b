package com.chaos.route.server.command;

import cn.hutool.json.JSONUtil;
import com.chaos.keep.alive.common.core.constant.CommandType;
import com.chaos.keep.alive.common.core.constant.Constants;
import com.chaos.keep.alive.common.core.constant.MessageType;
import com.chaos.keep.alive.common.core.domain.Session;
import com.chaos.keep.alive.common.core.util.ChannelUtils;
import com.chaos.keep.alive.common.core.util.DateTimeUtils;
import com.chaos.keep.alive.common.protobuf.Command;
import com.chaos.keep.alive.common.protobuf.OnlineRequest;
import com.chaos.keep.alive.common.protobuf.Result;
import com.chaos.route.gateway.GatewayRegistry;
import com.chaos.route.properties.ConfigProperties;
import com.chaos.security.api.authentication.resp.OperatorRespDTO;
import com.chaos.security.api.token.SupMobileTokenFacade;
import com.chaos.security.api.token.req.SupAuthTokenReqDTO;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.socket.SocketChannel;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.Executor;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OnlineServerCommandHandler implements ServerCommandHandler {


    private final GatewayRegistry gatewayRegistry;

    private final RedisTemplate<String, Session> redisTemplate;

    private final Executor taskExecutor;

    private final ConfigProperties configProperties;

    @Autowired
    private SupMobileTokenFacade supMobileTokenFacade;


    @SneakyThrows
    @Override
    public void handleCommand(Command command, ChannelHandlerContext ctx) {
        taskExecutor.execute(() -> {
            try {
                String gatewayId = gatewayRegistry.getGatewayId(ctx);
                OnlineRequest onlineRequest = OnlineRequest.parseFrom(command.getBody());
                String operatorNo = command.getOperatorNo();
                String deviceId = command.getDeviceId();

                boolean authResult = true;

                SupAuthTokenReqDTO supAuthTokenReqDTO = new SupAuthTokenReqDTO();
                supAuthTokenReqDTO.setAccessToken(onlineRequest.getToken());
                supAuthTokenReqDTO.setLoginName(onlineRequest.getLoginName());
                supAuthTokenReqDTO.setAppId(command.getAppId());
                supAuthTokenReqDTO.setDeviceId(command.getDeviceId());
                supAuthTokenReqDTO.setAppNo(onlineRequest.getAppNo());

                if(configProperties.getAuthenticate()) {
                    log.info("发起鉴权请求,SupAuthTokenReqDTO:{}", JSONUtil.toJsonStr(supAuthTokenReqDTO));
                    OperatorRespDTO respDTO = supMobileTokenFacade.auth(supAuthTokenReqDTO);
                    authResult = respDTO.getOperatorNo() != null;
                }
                if (authResult) {
                    Session session = new Session();
                    session.setGatewayId(gatewayId);
                    session.setTimestamp(DateTimeUtils.currentDateTime());
                    session.setDeviceId(deviceId);
                    session.setOperatorNo(command.getOperatorNo());
                    redisTemplate.boundHashOps(Constants.REDIS_SESSION_KEY + "::" + operatorNo).put(command.getDeviceId(), session);
                    log.info("客户端鉴权成功:{}", JSONUtil.toJsonStr(session));

                    Command newCommand = getCommand(command, authResult);
                    // 把writeAndFlush操作放入eventLoop中执行，避免线程切换
                    ctx.executor().execute(() -> ctx.writeAndFlush(newCommand));
                }
            } catch (Exception e) {
                Command newCommand = getCommand(command, false);
                ctx.executor().execute(() -> ctx.writeAndFlush(newCommand));
                log.error(e.getMessage(), e);
            }
        });
    }


    private Command getCommand(Command command, boolean result) {
        return Command.newBuilder()
                .setBizType(command.getBizType())
                .setMessageType(MessageType.MESSAGE_TYPE_PUSH)
                .setOperatorNo(command.getOperatorNo())
                .setDeviceId(command.getDeviceId())
                .setBody(Result.newBuilder()
                        .setSuccess(result)
                        .build().toByteString())
                .build();
    }

    @Override
    public int getType() {
        return CommandType.COMMAND_ONLINE;
    }
}
