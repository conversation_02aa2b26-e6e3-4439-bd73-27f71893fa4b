package com.chaos.route.server.command;

import com.chaos.keep.alive.common.core.constant.CommandType;
import com.chaos.keep.alive.common.core.constant.Constants;
import com.chaos.keep.alive.common.core.domain.Session;
import com.chaos.keep.alive.common.protobuf.Command;
import io.netty.channel.ChannelHandlerContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OfflineServerCommandHandler implements ServerCommandHandler {

    private final RedisTemplate<String, Session> redisTemplate;

    private final Executor taskExecutor;

    @Override
    public void handleCommand(Command command, ChannelHandlerContext ctx) {
        taskExecutor.execute(() -> {
            try {
                // 下线后删除session
                log.info("客户端下线删除session:{}", command.getOperatorNo() + ":" + command.getDeviceId());
                redisTemplate.boundHashOps(Constants.REDIS_SESSION_KEY + "::" + command.getOperatorNo()).delete(String.valueOf(command.getDeviceId()));
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
    }

    @Override
    public int getType() {
        return CommandType.COMMAND_OFFLINE;
    }
}
