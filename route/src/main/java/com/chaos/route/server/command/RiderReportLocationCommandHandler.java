package com.chaos.route.server.command;

import cn.hutool.json.JSONUtil;
import com.chaos.keep.alive.common.core.constant.CommandType;
import com.chaos.keep.alive.common.core.constant.MessageType;
import com.chaos.keep.alive.common.core.domain.RiderReportLocationJsonRequest;
import com.chaos.keep.alive.common.protobuf.Command;
import com.chaos.keep.alive.common.protobuf.Result;
import com.chaos.keep.alive.common.protobuf.RiderReportLocationRequest;
import com.chaos.route.util.RouteContext;
import io.netty.channel.ChannelHandlerContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.util.UUID;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RiderReportLocationCommandHandler implements ServerCommandHandler {

    private final KafkaTemplate<String, String> kafkaTemplate;

    @Value("#{'topic-wownow-rider-report-location-'.concat('${spring.profiles.active}')}")
    private String TOPIC_RIDER_REPORT_LOCATION;

    private final Executor taskExecutor;


    private final RouteContext routeContext;

    @Override
    public void handleCommand(Command command, ChannelHandlerContext ctx) {
        taskExecutor.execute(() -> {
            try {
                RiderReportLocationRequest body = RiderReportLocationRequest.parseFrom(command.getBody());

                RiderReportLocationJsonRequest request = new RiderReportLocationJsonRequest();
                request.setAppId(command.getAppId());
                request.setTimestamp(System.currentTimeMillis());
                request.setDeviceId(command.getDeviceId());
                request.setMessageId(UUID.randomUUID().toString().replace("-",""));
                request.setRiderId(body.getRiderId());
                request.setLongitude(body.getLongitude());
                request.setLatitude(body.getLatitude());
                if (CommandType.COMMAND_WOWNOW_RIDER_REPORT_LOCATION ==command.getBizType()) {
                    kafkaTemplate.send(TOPIC_RIDER_REPORT_LOCATION, String.valueOf(request.getRiderId()), JSONUtil.toJsonStr(request));
                    log.info("骑手上报位置数据到kafka:{}",JSONUtil.toJsonStr(request));
                }

                Result  result = Result.newBuilder()
                        .setSuccess(true)
                        .setErrorCode("000000")
                        .setErrorMessage("上报位置成功").build();
               Command response = Command.newBuilder()
                        .setAppSdkVersion(command.getAppSdkVersion())
                        .setMessageType(MessageType.MESSAGE_TYPE_PUSH)
                        .setBizType(command.getBizType())
                        .setTimestamp(command.getTimestamp())
                        .setOperatorNo(command.getOperatorNo())
                        .setDeviceId(command.getDeviceId())
                        .setAppId(command.getAppId())
                        .setBody(result.toByteString())
                       .build();
                ctx.writeAndFlush(response);

            } catch (Exception e) {
                log.error(e.getMessage(), e);
                Result  result = Result.newBuilder()
                        .setSuccess(false)
                        .setErrorCode("000000")
                        .setErrorMessage("上报位置失败,原因:"+e.getMessage()).build();
                Command response = Command.newBuilder()
                        .setAppSdkVersion(command.getAppSdkVersion())
                        .setMessageType(MessageType.MESSAGE_TYPE_PUSH)
                        .setBizType(command.getBizType())
                        .setTimestamp(command.getTimestamp())
                        .setOperatorNo(command.getOperatorNo())
                        .setDeviceId(command.getDeviceId())
                        .setAppId(command.getAppId())
                        .setBody(result.toByteString())
                        .build();
                ctx.writeAndFlush(response);
            }
        });
    }

    @Override
    public int getType() {
        return CommandType.COMMAND_WOWNOW_RIDER_REPORT_LOCATION;
    }
}
