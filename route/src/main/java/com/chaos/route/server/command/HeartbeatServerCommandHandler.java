package com.chaos.route.server.command;

import com.chaos.keep.alive.common.core.constant.CommandType;
import com.chaos.keep.alive.common.protobuf.Command;
import com.chaos.route.gateway.GatewayRegistry;
import io.netty.channel.ChannelHandlerContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class HeartbeatServerCommandHandler implements ServerCommandHandler {

    private final GatewayRegistry gatewayRegistry;

    @Override
    public void handleCommand(Command command, ChannelHandlerContext ctx) {
        String gatewayId = gatewayRegistry.getGatewayId(ctx);
        log.debug("收到[{}]的心跳", gatewayId);
        ctx.writeAndFlush(command);
    }

    @Override
    public int getType() {
        return CommandType.COMMAND_HEARTBEAT;
    }
}
