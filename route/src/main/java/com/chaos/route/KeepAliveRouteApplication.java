package com.chaos.route;

import com.chaos.route.server.RouteServer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;

@SpringBootApplication
public class KeepAliveRouteApplication implements ApplicationListener<ApplicationReadyEvent> {

    @Autowired
    private RouteServer routeServer;

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(KeepAliveRouteApplication.class);
        application.setWebApplicationType(WebApplicationType.SERVLET);
        application.run(args);
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent applicationReadyEvent) {
        routeServer.start();
    }

}
