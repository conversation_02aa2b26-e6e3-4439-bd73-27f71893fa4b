package com.chaos.route.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.chaos.keep.alive.common.core.constant.Constants;
import com.chaos.keep.alive.common.core.constant.MessageType;
import com.chaos.keep.alive.common.core.domain.MessageJsonPush;
import com.chaos.keep.alive.common.core.domain.Session;
import com.chaos.keep.alive.common.im.constant.CategoryType;
import com.chaos.keep.alive.common.im.constant.CommandType;
import com.chaos.keep.alive.common.im.domain.MessageSendJsonResponse;
import com.chaos.keep.alive.common.protobuf.Command;
import com.chaos.keep.alive.common.protobuf.ImMessagePush;
import com.chaos.keep.alive.common.protobuf.MessagePush;
import com.chaos.keep.alive.common.protobuf.MessageSendResponse;
import com.chaos.message.push.api.constant.PushMQConsts;
import com.chaos.message.push.api.dto.PushReqDTO;
import com.chaos.notification.api.enums.NotifyTypeEnum;
import com.chaos.route.gateway.GatewayRegistry;
import com.chaos.usercenter.api.UserOperatorFacade;
import com.chaos.usercenter.api.dto.resp.UserOperatorInfoForImDTO;
import com.google.common.collect.Maps;
import com.outstanding.framework.plugin.mq.rocketmq.core.RocketMQTemplate;
import io.netty.channel.ChannelHandlerContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.rocketmq.client.producer.SendCallback;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MessageHandler {

    private final RedisTemplate<String, Session> redisTemplate;

    private final GatewayRegistry registry;

    @Autowired
    private UserOperatorFacade userOperatorFacade;

    public static final String IM_PUSH_TEMPLATE = "PMO099";


    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(
            5, 20, 60, TimeUnit.SECONDS,
            new SynchronousQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());


    /**
     * 处理消息推送
     */
    @KafkaListener(topics = "#{'topic-gonow-order-status-change-'.concat('${spring.profiles.active}')}", properties = {
            "max.poll.interval.ms:600000",
            ConsumerConfig.MAX_POLL_RECORDS_CONFIG + "=1000"
    })
    public void handleGoNowOrderStatusMessagePush(List<ConsumerRecord<String, String>> records, Acknowledgment ack) {
        // 对要推送的用户分组

        Map<String, List<MessageJsonPush>> groupByOperatorNo = records.stream().map(record -> JSONObject.parseObject(record.value(), MessageJsonPush.class))
                .collect(Collectors.groupingBy(MessageJsonPush::getToOperatorNo));
        for (Map.Entry<String, List<MessageJsonPush>> entry : groupByOperatorNo.entrySet()) {
            executor.execute(() -> {
                // 分组后可以只取一次session
                String operatorNo = entry.getKey();
                Map<Object, Object> clients = redisTemplate.boundHashOps(Constants.REDIS_SESSION_KEY + "::" + operatorNo).entries();
                if (CollUtil.isNotEmpty(clients)) {
                    // 遍历client
                    for (Object object : clients.values()) {
                        // 获取到session
                        Session session = (Session) object;
//                        log.info("get session from redis:"+ JSONUtil.toJsonStr(session));
                        ChannelHandlerContext ctx = registry.getChannel(session.getGatewayId());
                        if (Objects.nonNull(ctx)) {
                            // 如果有ctx，发送消息发送响应信息
                            List<MessageJsonPush> pushList = entry.getValue();
                            for (MessageJsonPush jsonPush : pushList) {
                                MessagePush response = MessagePush.newBuilder()
//                                        .setBizType(jsonPush.getBizType())
//                                        .setMessageId(jsonPush.getMessageId())
//                                        .setMessageType(jsonPush.getBizType())
                                        .setContent(jsonPush.getContent())
                                        .build();

                                Command command = Command.newBuilder()
                                        .setOperatorNo(session.getOperatorNo())
                                        .setDeviceId(session.getDeviceId())
                                        .setBizType(jsonPush.getBizType())
                                        .setBody(response.toByteString())
                                        .build();
                                log.info("接收消息[bizType:{},operatorNo:{},deviceId:{},messageId:{},toOperateNo:{},content:{},body:{}]",
                                        jsonPush.getBizType(), session.getOperatorNo(), session.getDeviceId(),
                                        jsonPush.getMessageId(), jsonPush.getToOperatorNo(), jsonPush.getContent(), command.getBody());
                                ctx.executor().execute(() -> ctx.writeAndFlush(command));
                            }
                        }
                    }
                }
            });
        }
        ack.acknowledge();

    }


    @KafkaListener(topics = "#{'topic-gonow-order-basic-info-'.concat('${spring.profiles.active}')}", properties = {
            "max.poll.interval.ms:600000",
            ConsumerConfig.MAX_POLL_RECORDS_CONFIG + "=1000"
    })
    public void handleGoNowOrderBasicInfoMessagePush(List<ConsumerRecord<String, String>> records, Acknowledgment ack) {
        // 对要推送的用户分组

        Map<String, List<MessageJsonPush>> groupByOperatorNo = records.stream().map(record -> JSONObject.parseObject(record.value(), MessageJsonPush.class))
                .collect(Collectors.groupingBy(MessageJsonPush::getToOperatorNo));
        for (Map.Entry<String, List<MessageJsonPush>> entry : groupByOperatorNo.entrySet()) {
            executor.execute(() -> {
                // 分组后可以只取一次session
                String operatorNo = entry.getKey();
                Map<Object, Object> clients = redisTemplate.boundHashOps(Constants.REDIS_SESSION_KEY + "::" + operatorNo).entries();
                if (CollUtil.isNotEmpty(clients)) {
                    // 遍历client
                    for (Object object : clients.values()) {
                        // 获取到session
                        Session session = (Session) object;
//                        log.info("get session from redis:"+ JSONUtil.toJsonStr(session));
                        ChannelHandlerContext ctx = registry.getChannel(session.getGatewayId());
                        if (Objects.nonNull(ctx)) {
                            // 如果有ctx，发送消息发送响应信息
                            List<MessageJsonPush> pushList = entry.getValue();
                            for (MessageJsonPush jsonPush : pushList) {
                                MessagePush response = MessagePush.newBuilder()
//                                        .setBizType(jsonPush.getBizType())
//                                        .setMessageId(jsonPush.getMessageId())
//                                        .setMessageType(jsonPush.getMessageType())
                                        .setContent(jsonPush.getContent())
                                        .build();

                                Command command = Command.newBuilder()
                                        .setOperatorNo(session.getOperatorNo())
                                        .setDeviceId(session.getDeviceId())
                                        .setBizType(jsonPush.getBizType())
                                        .setBody(response.toByteString())
                                        .build();
                                log.debug("接收订单基础消息[bizType:{},operatorNo:{},deviceId:{},messageId:{},toOperateNo:{},content:{}]",
                                        jsonPush.getBizType(), session.getOperatorNo(), session.getDeviceId(),
                                        jsonPush.getMessageId(), jsonPush.getToOperatorNo(), jsonPush.getContent());
                                ctx.executor().execute(() -> ctx.writeAndFlush(command));
                            }
                        }
                    }
                }
            });
        }
        ack.acknowledge();

    }


    /**
     * 处理发送消息响应
     */
    @KafkaListener(topics = "#{'topic-message-send-response-'.concat('${spring.profiles.active}')}")
    public void handleMessageSendResponse(List<ConsumerRecord<String, String>> records, Acknowledgment ack) {
        // 对要推送的用户分组
        Map<String, List<MessageSendJsonResponse>> groupByFromId = records.stream().map(record -> JSONObject.parseObject(record.value(), MessageSendJsonResponse.class))
                .collect(Collectors.groupingBy(MessageSendJsonResponse::getFromOperatorNo));
        for (Map.Entry<String, List<MessageSendJsonResponse>> entry : groupByFromId.entrySet()) {

            // 分组后可以只取一次session
            String userId = entry.getKey();
            Map<Object, Object> clients = redisTemplate.boundHashOps(Constants.REDIS_SESSION_KEY + "::" + userId).entries();
            if (CollUtil.isNotEmpty(clients)) {
                // 遍历client
                for (Object object : clients.values()) {
                    // 获取到session
                    Session session = (Session) object;
                    ChannelHandlerContext ctx = registry.getChannel(session.getGatewayId());
                    if (Objects.nonNull(ctx)) {
                        // 如果有ctx，发送消息发送响应信息
                        List<MessageSendJsonResponse> responses = entry.getValue();
                        for (MessageSendJsonResponse jsonResponse : responses) {

                            int bizType = jsonResponse.getChatType().equals(com.chaos.keep.alive.common.im.constant.Constants.CHAT_TYPE_C2G)?
                                    CommandType.COMMAND_IM_C2G_MESSAGE_SEND : CommandType.COMMAND_IM_C2C_MESSAGE_SEND;

                            MessageSendResponse response = MessageSendResponse.newBuilder()
                                    .setMessageId(jsonResponse.getMessageId())
                                    .setChatId(jsonResponse.getChatId())
                                    .setChatType(jsonResponse.getChatType())
                                    .setFromOperatorNo(jsonResponse.getFromOperatorNo())
                                    .setSequence(jsonResponse.getSequence())
                                    .setToOperatorNo(jsonResponse.getToOperatorNo())
                                    .setTimestamp(System.currentTimeMillis())
                                    .build();

                            Command command = Command.newBuilder()
                                    .setOperatorNo(session.getOperatorNo())
                                    .setDeviceId(session.getDeviceId())
                                    .setBizType(bizType)
                                    .setMessageType(com.chaos.keep.alive.common.im.constant.MessageType.MESSAGE_TYPE_SERVER_ACK)
                                    .setBody(response.toByteString())
                                    .build();
                            log.info("发送消息响应[bizType:{},operatorNo:{},deviceId:{},messageId:{},toOperateNo:{},messageType:{}]",
                                    command.getBizType(), command.getOperatorNo(), command.getDeviceId(), response.getMessageId(),
                                    response.getToOperatorNo(),command.getMessageType());
                            ctx.writeAndFlush(command);
                        }
                    }
                }
            }

        }
        ack.acknowledge();
    }


    /**
     * 处理消息推送
     */
    @KafkaListener(topics = "#{'topic-message-push-'.concat('${spring.profiles.active}')}", properties = {
            "max.poll.interval.ms:600000",
            ConsumerConfig.MAX_POLL_RECORDS_CONFIG + "=1000"
    })
    public void handleMessagePush(List<ConsumerRecord<String, String>> records, Acknowledgment ack) {
        // 对要推送的用户分组
        Map<String, List<com.chaos.keep.alive.common.im.domain.MessageJsonPush>> groupByToId = records.stream().map(record -> JSONObject.parseObject(record.value(), com.chaos.keep.alive.common.im.domain.MessageJsonPush.class))
                .collect(Collectors.groupingBy(com.chaos.keep.alive.common.im.domain.MessageJsonPush::getToOperatorNo));
        for (Map.Entry<String, List<com.chaos.keep.alive.common.im.domain.MessageJsonPush>> entry : groupByToId.entrySet()) {
            executor.execute(() -> {
                // 分组后可以只取一次session
                String operatorNo = entry.getKey();
                Map<Object, Object> clients = redisTemplate.boundHashOps(Constants.REDIS_SESSION_KEY + "::" + operatorNo).entries();
                if (CollUtil.isNotEmpty(clients)) {
                    pushOnlineMsg(entry, clients);
                }else{
                    //长连接推送消息失败,需要通过消息中台推送离线消息
                    // 参数设置
                    pushOfflineMsg(entry, operatorNo);
                }
            });
        }
        ack.acknowledge();
    }

    private void pushOnlineMsg(Map.Entry<String, List<com.chaos.keep.alive.common.im.domain.MessageJsonPush>> entry, Map<Object, Object> clients) {
        // 遍历client
        for (Object object : clients.values()) {
            // 获取到session
            Session session = (Session) object;
            ChannelHandlerContext ctx = registry.getChannel(session.getGatewayId());
            if (Objects.nonNull(ctx)) {
                // 如果有ctx，发送消息发送响应信息
                List<com.chaos.keep.alive.common.im.domain.MessageJsonPush> pushList = entry.getValue();
                for (com.chaos.keep.alive.common.im.domain.MessageJsonPush jsonPush : pushList) {
                    log.info("get jsonPush Object : {}", JSONUtil.toJsonStr(jsonPush));
                    ImMessagePush response = ImMessagePush.newBuilder()
                            .setMessageId(jsonPush.getMessageId())
                            .setContent(jsonPush.getContent())
                            .setFromOperatorNo(jsonPush.getFromOperatorNo())
                            .setToOperatorNo(jsonPush.getToOperatorNo())
                            .setCategory(jsonPush.getCategory())
                            .setChatType(jsonPush.getChatType())
                            .setChatId(jsonPush.getChatId())
                            .setSequence(jsonPush.getSequence())
                            .build();
                    Command.Builder builder = Command.newBuilder();
                    if (Objects.equals(jsonPush.getChatType(), com.chaos.keep.alive.common.im.constant.Constants.CHAT_TYPE_C2C)) {

                        builder.setOperatorNo(session.getOperatorNo())
                                .setDeviceId(session.getDeviceId())
                                .setBizType(CommandType.COMMAND_IM_C2C_MESSAGE_SEND)
                                .setMessageType(MessageType.MESSAGE_TYPE_PUSH)
                                .setTimestamp(jsonPush.getTimestamp())
                                .setBody(response.toByteString());
                    } else {

                        builder.setOperatorNo(session.getOperatorNo())
                                .setDeviceId(session.getDeviceId())
                                .setBizType(CommandType.COMMAND_IM_C2G_MESSAGE_SEND)
                                .setMessageType(MessageType.MESSAGE_TYPE_PUSH)
                                .setTimestamp(jsonPush.getTimestamp())
                                .setBody(response.toByteString());
                    }
                    log.info("接收到push消息[chatType:{},messageId:{},fromOperatorNo:{},toOperatorNo:{},sequence:{},content:{},chatId:{},category:{}]",
                            jsonPush.getChatType(), jsonPush.getMessageId(), jsonPush.getFromOperatorNo(), jsonPush.getToOperatorNo(), jsonPush.getSequence(),
                            jsonPush.getContent(), jsonPush.getChatId(), jsonPush.getCategory());
                    ctx.executor().execute(() -> ctx.writeAndFlush(builder.build()));
                }
            }
        }
    }

    private void pushOfflineMsg(Map.Entry<String, List<com.chaos.keep.alive.common.im.domain.MessageJsonPush>> entry, String operatorNo) {
        UserOperatorInfoForImDTO operatorInfoRespDTO =  userOperatorFacade.getOperatorInfoForIM(operatorNo);
        String title = operatorInfoRespDTO.getNickName();

        Map<String, Object> titleParam = Maps.newHashMap();
        titleParam.put("titleParams", title);


        List<com.chaos.keep.alive.common.im.domain.MessageJsonPush> pushList = entry.getValue();
        for (com.chaos.keep.alive.common.im.domain.MessageJsonPush jsonPush : pushList) {

            PushReqDTO pushReqDTO = getPushReqDTO(jsonPush, operatorNo, operatorInfoRespDTO, titleParam);

            rocketMQTemplate.asyncSend(PushMQConsts.PUSH_TOPIC, pushReqDTO, new SendCallback() {


                @Override
                public void onSuccess(org.apache.rocketmq.client.producer.SendResult sendResult) {
                    log.info("IM消息推送MQ发送成功:{}", JSONUtil.toJsonStr(pushReqDTO));
                }

                @Override
                public void onException(Throwable throwable) {
                    log.error("IM消息推送MQ发送异常", throwable);
                }
            });

        }

        log.info("WOWNOWIM消息推送结束");
    }

    private static PushReqDTO getPushReqDTO(com.chaos.keep.alive.common.im.domain.MessageJsonPush jsonPush, String operatorNo, UserOperatorInfoForImDTO operatorInfoRespDTO, Map<String, Object> titleParam) {
        cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(jsonPush.getContent());

        Integer category = jsonPush.getCategory();
        String content = getContentByCategory(category, jsonObject);

        Map<String, Object> contentParam = Maps.newHashMap();
        contentParam.put("contentParams", content == null ? "" : (content.length() > 30 ? content.substring(0, 30) + "..." : content));

        Map<String, Object> customContent = Maps.newHashMap();
        customContent.put("groupId",jsonPush.getGroupId()+"");
        customContent.put("chatId", jsonPush.getChatId()+"");
        customContent.put("chatType", jsonPush.getChatType()+"");
        customContent.put("toOperatorNo", jsonPush.getToOperatorNo());
        customContent.put("fromOperatorNo", jsonPush.getFromOperatorNo());
        customContent.put("originContent",jsonPush.getContent());


        String ex = (String)  jsonObject.get("ex");
        if(StringUtils.isNotEmpty(ex) ){
            String type = (String) JSONUtil.parseObj(ex).get("type");
            if("voiceCall".equals(type)){
                customContent.put("sound","calling");
                customContent.put("soundIos","answer_bell.wav");
                customContent.put("channel_id","im_voice_call_config");
                customContent.put("callType",type);
            }
            if("voiceCallReject".equals(type)){
                customContent.put("soundIos","hangup_bell.wav");
                customContent.put("callType",type);
            }
        }


        PushReqDTO pushReqDTO = new PushReqDTO();
        pushReqDTO.setTemplateNo(IM_PUSH_TEMPLATE);
        pushReqDTO.setNotifyType(NotifyTypeEnum.NOTIFY_SINGLE);
        pushReqDTO.setMessageNo(IdUtil.simpleUUID());
        pushReqDTO.setUserNo(operatorNo);
        pushReqDTO.setAppId(operatorInfoRespDTO.getAppId());
        pushReqDTO.setTitleParams(titleParam);
        pushReqDTO.setContentParams(contentParam);
        pushReqDTO.setCustomContent(customContent);
        return pushReqDTO;
    }

    private static String getContentByCategory(Integer category, cn.hutool.json.JSONObject jsonObject) {
        String content  = "";
        if(CategoryType.TEXT.equals(category)){
            content =  (String) jsonObject.get("text");
            String ex = (String)  jsonObject.get("ex");
            if(StringUtils.isNotEmpty(ex) ){
              String type = (String) JSONUtil.parseObj(ex).get("type");
              if("voiceCallReject".equals(type)||"voiceCall".equals(type)){
                  content  = "[语音通话]";
              }
            }
        }else if(CategoryType.VOICE.equals(category)){
            content =  "[语音]";
        } else if (CategoryType.FILE.equals(category)) {
           content  = "[文件]";
        }else if(CategoryType.VIDEO.equals(category)) {
            content = "[视频]";
        }else if(CategoryType.PICTURE.equals(category)) {
            content = "[图片]";
        }else if(CategoryType.LOCATION.equals(category)) {
            content = "[位置]";
        }else if(CategoryType.PRODUCT_CARD.equals(category)) {
            content = "[商品卡片]";
        }else if(CategoryType.STORE_CARD.equals(category)) {
            content = "[店铺卡片]";
        }else if(CategoryType.ORDER_CARD.equals(category)) {
            content = "[订单卡片]";
        }
        return content;
    }


}
