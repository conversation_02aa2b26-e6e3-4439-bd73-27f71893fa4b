package com.chaos.route.properties;

import com.chaos.keep.alive.common.core.properties.ZookeeperProperties;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = ConfigProperties.PREFIX)
@Component
@Getter
@Setter
public class ConfigProperties {

    public static final String PREFIX = "ka";

    /**
     * 服务Id
     */
    private String serverId;

    private Integer port;

    /**
     * 心跳配置
     */
    private HeartbeatProperties heartbeat = new HeartbeatProperties();

    /**
     * zookeeper配置
     */
    private ZookeeperProperties zk = new ZookeeperProperties();

    private Boolean authenticate = true;
}
