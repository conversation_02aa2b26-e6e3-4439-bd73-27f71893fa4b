package com.chaos.route.util;

import cn.hutool.core.util.StrUtil;
import com.chaos.keep.alive.common.core.constant.ZkConstants;
import com.chaos.keep.alive.common.core.exception.SystemException;
import com.chaos.route.properties.ConfigProperties;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.InetAddress;

/**
 * <AUTHOR>
 */

@Component
@RequiredArgsConstructor
public class RouteContext {

    private final ConfigProperties configProperties;

    @Value("${spring.profiles.active}")
    private String profile;

    @SneakyThrows
    public String serverId() {
        String serverId = System.getenv("im.serverId");
        if (StrUtil.isBlank(serverId)) {
            serverId = configProperties.getServerId();
            if (StrUtil.isBlank(serverId)) {
                throw new SystemException("serverId不能为空");
            }
        }
        return serverId;
    }

    public Integer getPort() {
        return configProperties.getPort();
    }

    public String getZkRootPath() {
        return ZkConstants.ZK_ROUTE_PATH + "/" + profile;
    }

    @SneakyThrows
    public String getZkPath() {
        return getZkRootPath()
                + "/" + serverId() + ":" + InetAddress.getLocalHost().getHostAddress()
                + ":" + this.getPort();
    }
}
