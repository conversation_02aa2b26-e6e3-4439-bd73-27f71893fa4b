dubbo.application.name=ka-route
dubbo.registry.address=zookeeper://zk-dubbo-0.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181?backup=zk-dubbo-1.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181,zk-dubbo-2.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181
dubbo.protocol.port=20990
dubbo.provider.group=chaos
dubbo.consumer.group=chaos
ka.serverId=keep-alive-route
ka.port=9570
ka.authenticate=false
ka.heartbeat.readTimeout=30000
ka.zk.enable=true
ka.zk.retry=3
ka.zk.intervalTime=1000
ka.zk.zkServer=svc-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181
server.port=9099
spring.main.banner-mode=off
spring.main.allow-bean-definition-overriding=true
spring.application.name=keep-alive-route
spring.redis.cluster.nodes=drc-redis-0-0.redis-svc-0.lifekh-tool-sit.svc.cluster.local:6379,drc-redis-0-1.redis-svc-0.lifekh-tool-sit.svc.cluster.local:6379,drc-redis-1-0.redis-svc-1.lifekh-tool-sit.svc.cluster.local:6379,drc-redis-1-1.redis-svc-1.lifekh-tool-sit.svc.cluster.local:6379,drc-redis-2-0.redis-svc-2.lifekh-tool-sit.svc.cluster.local:6379,drc-redis-2-1.redis-svc-2.lifekh-tool-sit.svc.cluster.local:6379
spring.kafka.producer.bootstrap-servers=172.16.27.12:29092
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.consumer.bootstrap-servers=172.16.27.12:29092
spring.kafka.consumer.group-id=ka-route
spring.kafka.consumer.auto-offset-reset=latest
spring.kafka.consumer.enable-auto-commit=false
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.listener.type=batch
spring.kafka.listener.ack-mode=manual_immediate
