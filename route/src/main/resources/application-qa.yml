server:
  port: 8080
spring:
  main:
    banner-mode: "off"
    allow-bean-definition-overriding: true
  application:
    name: keep-alive-route
  redis:
    cluster:
      nodes: drc-tw-redis-0-0.tw-redis-svc-0.lifekh-tool-uat.svc.cluster.local:6379,drc-tw-redis-0-1.tw-redis-svc-0.lifekh-tool-uat.svc.cluster.local:6379,drc-tw-redis-1-0.tw-redis-svc-1.lifekh-tool-uat.svc.cluster.local:6379,drc-tw-redis-1-1.tw-redis-svc-1.lifekh-tool-uat.svc.cluster.local:6379,drc-tw-redis-2-0.tw-redis-svc-2.lifekh-tool-uat.svc.cluster.local:6379,drc-tw-redis-2-1.tw-redis-svc-2.lifekh-tool-uat.svc.cluster.local:6379

  kafka:
    producer:
      bootstrap-servers: 172.16.27.10:39092,172.16.27.11:39092,172.16.27.12:39092
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      bootstrap-servers: 172.16.27.10:39092,172.16.27.11:39092,172.16.27.12:39092
      group-id: ka-route
      auto-offset-reset: latest
      enable-auto-commit: false
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    listener:
      type: batch
      ack-mode: manual_immediate
ka:
  serverId: keep-alive-route
  port: 9570
  authenticate: false
  heartbeat:
    readTimeout: 30000
  zk:
    enable: true
    retry: 3
    intervalTime: 1000
    zkServer: svc-zk-dubbo.lifekh-tool-uat.svc.cluster.local:2181
#logging:
#  path: D://log
#  level:
#    com: debug