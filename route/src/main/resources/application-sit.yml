server:
  port: 9099
spring:
  main:
    banner-mode: "off"
    allow-bean-definition-overriding: true
  application:
    name: keep-alive-route
  redis:
    host: drc-redis-0-0.redis-svc-0.lifekh-tool-sit.svc.cluster.local
    port: 6379
#    password: abc.1234
    timeout: 10000
#    database: 2
    lettuce:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0
  kafka:
    producer:
      bootstrap-servers: ************:39092,************:39092,************:39092
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      bootstrap-servers: ************:39092,************:39092,************:39092
      group-id: ka-route
      auto-offset-reset: latest
      enable-auto-commit: false
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    listener:
      type: batch
      ack-mode: manual_immediate
ka:
  serverId: keep-alive-route
  port: 9570
  authenticate: false
  heartbeat:
    readTimeout: 30000
  zk:
    enable: true
    retry: 3
    intervalTime: 1000
    zkServer: svc-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181
logging:
  path: D://log
  level:
    com: debug