<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans-4.1.xsd
    http://www.springframework.org/schema/context
    http://www.springframework.org/schema/context/spring-context-4.1.xsd
    http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <context:component-scan base-package="com.chaos"/>
    <dubbo:reference interface="com.chaos.security.api.token.SupMobileTokenFacade" id="supMobileTokenFacade" version="1.0.0"></dubbo:reference>
    <dubbo:reference interface="com.chaos.usercenter.api.UserOperatorFacade" id="userOperatorFacade" version="1.0.0"/>

</beans>
