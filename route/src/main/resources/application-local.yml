server:
  port: 9097
spring:
  main:
    banner-mode: "off"
    allow-bean-definition-overriding: true
  application:
    name: gonow-ka-route
  redis:
    host: *************
#    host: localhost
    port: 6379
  kafka:
    producer:
      bootstrap-servers: ************:39092,************:39092,************:39092
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      bootstrap-servers: ************:39092,************:39092,************:39092
      group-id: ka-route
      auto-offset-reset: latest
      enable-auto-commit: false
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    listener:
      type: batch
      ack-mode: manual_immediate
ka:
  serverId: keep-alive-route
  port: 9670
  authenticate: false
  heartbeat:
    readTimeout: 30000
  zk:
    enable: true
    retry: 3
    intervalTime: 1000
    zkServer: *************:2181
#    zkServer: localhost:2181
dubbo:
  application:
    name: driver-trajectory
  registry:
#    address: zookeeper://svc-zk-dubbo.lifekh-tool-uat.svc.cluster.local:2181
    address: zookeeper://*************:2181
#    address: zookeeper://zk-dubbo-0.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181?backup=zk-dubbo-1.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181,zk-dubbo-2.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181
  protocol:
    port: 20990
  provider:
    group: chaos
  consumer:
    group: chaos
logging:
#  path: D://log
  level:
    com: info
rocketmq:
  maxReconsumeTimes: 10
  name-server: svc-rocketmq-name-service.lifekh-tool-sit.svc.cluster.local:9876
  consumer:
    group: im-route-consumer
  producer:
    group: im-route-producer
