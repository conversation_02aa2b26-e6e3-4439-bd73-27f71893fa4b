#????log4j??????, ????false
log4j.debug=false

# ???logger, ??: [level],appenderName1,appenderName2,?
log4j.rootLogger=warn,console,errorDailyFile

# ??????logger, ??: log4j.logger.[????????]
log4j.logger.com.chenlongji=info,console,infoRollingFile
# ?????????logger
log4j.additivity.com.chenlongji=false

#################
# ??????
#################
#console ??????
log4j.appender.console=org.apache.log4j.ConsoleAppender
# ???????(??)???
log4j.appender.console.layout=org.apache.log4j.PatternLayout
# ?????????
log4j.appender.console.layout.conversionPattern=%d [%t] %-5p [%c\:%L] %m%n
# ??????? ??????ALL??
log4j.appender.console.threshold=info

#################
# ?????(RollingFileAppender??)
#################
# ???????, ?????????????????????
log4j.appender.infoRollingFile=org.apache.log4j.RollingFileAppender
# ??????? ??:./src/logs/clj2023.log
log4j.appender.infoRollingFile.file=D://logs/info.log
# ??????(?????????), ????10MB. ???????KB,MB,GB
log4j.appender.infoRollingFile.maxFileSize=500MB
# ???????(??)???. ?: layout?????
log4j.appender.infoRollingFile.layout=org.apache.log4j.PatternLayout
# ?????????
log4j.appender.infoRollingFile.layout.conversionPattern=%d [%t] %-5p [%c\:%L] %m%n
# ??????? ??????ALL??
log4j.appender.infoRollingFile.threshold=info

#################
# ?????(DailyRollingFileAppender??)
#################
# ?????????????
log4j.appender.errorDailyFile=org.apache.log4j.DailyRollingFileAppender
# ??????? ??:./src/logs/error.log
log4j.appender.errorDailyFile.file=D://logs/error.log
# ???????(??)???
log4j.appender.errorDailyFile.layout=org.apache.log4j.PatternLayout
# ?????????
log4j.appender.errorDailyFile.layout.conversionPattern=%d [%t] %-5p [%c\:%L] %m%n
# ???????, ?????????????????
log4j.appender.errorDailyFile.datePattern='.'yyyy-MM-dd'.log'
# ??????? ??????ALL??
log4j.appender.errorDailyFile.threshold=warn