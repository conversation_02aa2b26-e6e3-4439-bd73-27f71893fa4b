server:
  port: 8100
spring:
  main:
    banner-mode: "off"
    allow-bean-definition-overriding: true
  application:
    name: keep-alive-iplist
keep-alive:
  zk:
    enable: true
    retry: 3
    intervalTime: 1000
    zkServer: zk-dubbo-0.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181
dubbo:
  application:
    name: keep-alive-iplist
  registry:
    address: zookeeper://zk-dubbo-0.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181?backup=zk-dubbo-1.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181,zk-dubbo-2.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181
    #address: zookeeper://zk-dubbo-0.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181?backup=zk-dubbo-1.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181,zk-dubbo-2.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181
  protocol:
    port: 20990
  provider:
    group: chaos
  consumer:
    group: chaos
logging:
  path: D://log
  level:
    com: debug