package com.chaos.keep.alive.iplist.controller;

import cn.hutool.http.HttpStatus;
import com.chaos.keep.alive.common.core.domain.JsonResult;
import com.chaos.keep.alive.common.core.domain.address.AddressInstance;
import com.chaos.keep.alive.common.core.util.ResultHelper;
import com.chaos.keep.alive.iplist.gateway.AddressManager;
import com.outstanding.framework.core.ResponseDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ip")
@RequiredArgsConstructor
public class IpController {

    private final AddressManager addressManager;

    @GetMapping("/get")
    public ResponseDTO<?> get() {
        Optional<AddressInstance> optional = addressManager.get();
        if (optional.isPresent()) {
            return ResultHelper.ok(optional.get());
        } else {
            return ResultHelper.fail(String.valueOf(HttpStatus.HTTP_BAD_REQUEST), "没有可用的网关");
        }
    }
}
