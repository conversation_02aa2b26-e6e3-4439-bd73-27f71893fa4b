package com.chaos.keep.alive.iplist.properties;

import com.chaos.keep.alive.common.core.properties.ZookeeperProperties;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = ConfigProperties.PREFIX)
@Component
@Getter
@Setter
public class ConfigProperties {

    public static final String PREFIX = "keep-alive";

    /**
     * zookeeper配置
     */
    private ZookeeperProperties zk = new ZookeeperProperties();
}
