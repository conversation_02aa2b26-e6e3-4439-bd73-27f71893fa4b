<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.chaos</groupId>
        <artifactId>keep-alive-con</artifactId>
        <version>*******-SNAPSHOT</version>
    </parent>

    <groupId>com.chaos.im</groupId>
    <artifactId>iplist</artifactId>
    <version>*******-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>


    <dependencies>
        <dependency>
            <groupId>com.chaos</groupId>
            <artifactId>keep-alive-common-core</artifactId>
            <version>1.0.0</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.springframework.boot</groupId>-->
        <!--            <artifactId>spring-boot-starter-web</artifactId>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.outstanding</groupId>
            <artifactId>framework-container-dubbo</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.zookeeper</groupId>
                    <artifactId>zookeeper</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.zookeeper</groupId>
                    <artifactId>zookeeper-jute</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>com.outstanding</groupId>
                    <artifactId>framework-base</artifactId>
                </exclusion>

            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.outstanding</groupId>
            <artifactId>framework-base</artifactId>
            <exclusions>

                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-actuator</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-actuator-autoconfigure</artifactId>
                </exclusion>
                 <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-test</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-autoconfigure</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-test-autoconfigure</artifactId>
                </exclusion>


            </exclusions>

        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.springframework.boot</groupId>-->
        <!--            <artifactId>spring-boot-starter-actuator</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.chaos</groupId>
            <artifactId>keep-alive-common-web</artifactId>
            <version>1.0.1</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.yml</include>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
        <finalName>ka-iplist</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>