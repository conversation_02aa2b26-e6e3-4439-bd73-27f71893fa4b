keep-alive:
    heartbeat:
        heartbeatInterval: 2000
        readTimeout: 60000
    metrics:
        authInterval: 60000
        authLimit: 10
    port: 38888
    protocol: ws
    routeServers: 'null'
    serverId: gateway-tcp-1
    zk:
        enable: true
        intervalTime: 1000
        retry: 3
        zkServer: *************:2181
logging:
    level:
        root: info
server:
    port: 9094
spring:
    application:
        name: gateway-tcp
    main:
        allow-bean-definition-overriding: true
        banner-mode: 'off'
    redis:
        host: *************
        port: 6379