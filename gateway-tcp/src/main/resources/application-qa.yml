#server:
#  port: 8080
#spring:
#  main:
#    banner-mode: "off"
#    allow-bean-definition-overriding: true
#  application:
#    name: keep-alive-gateway-tcp
#keep-alive:
#  protocol: tcp
#  serverId: keep-alive-gateway-tcp
#  port: 38888
#  heartbeat:
#    heartbeatInterval: 5000
#    readTimeout: 30000
#  zk:
#    enable: true
#    retry: 3
#    intervalTime: 1000
#    zkServer: svc-zk-dubbo.lifekh-tool-uat.svc.cluster.local:2181
#  routeServers: