#dubbo:
#    application:
#        name: driver-trajectory
#    consumer:
#        group: chaos
#    protocol:
#        port: 20990
#    provider:
#        group: chaos
#    registry:
#        address: zookeeper://zk-dubbo-0.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181?backup=zk-dubbo-1.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181,zk-dubbo-2.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181
#keep-alive:
#    heartbeat:
#        heartbeatInterval: 5000
#        readTimeout: 30000
#    port: 38888
#    protocol: tcp
#    routeServers: 'null'
#    serverId: gateway-tcp-1
#    zk:
#        enable: true
#        intervalTime: 1000
#        retry: 3
#        zkServer: zk-dubbo-0.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181
#server:
#    port: 8098
#spring:
#    application:
#        name: gateway-tcp
#    main:
#        allow-bean-definition-overriding: true
#        banner-mode: 'off'
