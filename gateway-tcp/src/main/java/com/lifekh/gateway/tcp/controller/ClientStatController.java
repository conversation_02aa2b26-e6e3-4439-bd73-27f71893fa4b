package com.lifekh.gateway.tcp.controller;

import com.lifekh.gateway.tcp.client.ClientManager;
import com.outstanding.framework.core.ResponseDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/clientState")
public class ClientStatController {

    @Autowired
    private ClientManager clientManager;

    @GetMapping("/get")
    public ResponseDTO<?> getState() {

        return ResponseDTO.creatDTO(clientManager.printChannelId2Client());
    }

}
