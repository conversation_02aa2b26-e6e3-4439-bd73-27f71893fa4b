package com.lifekh.gateway.tcp.route.command;

import com.chaos.keep.alive.common.core.exception.SystemException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ClientCommandHandlerFactory {

    private final List<ClientCommandHandler> commandHandlers;

    public ClientCommandHandler getCommandHandler(int type) {
        for (ClientCommandHandler commandHandler : commandHandlers) {
            if (type == commandHandler.getType()) {
                return commandHandler;
            }
        }
        throw new SystemException("找不到命令处理器");
    }
}
