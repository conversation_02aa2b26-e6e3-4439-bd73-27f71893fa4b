package com.lifekh.gateway.tcp.util;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.CorruptedFrameException;
import io.netty.handler.codec.protobuf.ProtobufVarint32FrameDecoder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class WowNowKADecoder extends ProtobufVarint32FrameDecoder {

    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out)
            throws Exception {
        try {
            super.decode(ctx, in, out);
        }catch (CorruptedFrameException e){
            // 打印异常和当前缓冲区数据
            log.error("Decode Error:{} Raw Data (Hex):{} " , e.getMessage(),ByteBufUtil.hexDump(in));
            // 释放当前缓冲区（避免内存泄漏）
            in.release();

            // 关闭连接
//            ctx.close();
        }
    }
}
