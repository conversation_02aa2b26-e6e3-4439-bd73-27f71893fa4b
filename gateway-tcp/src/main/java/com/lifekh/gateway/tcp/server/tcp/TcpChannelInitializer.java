package com.lifekh.gateway.tcp.server.tcp;

import com.chaos.keep.alive.common.protobuf.Command;
import com.lifekh.gateway.tcp.properties.ConfigProperties;
import io.netty.channel.Channel;
import io.netty.channel.ChannelInitializer;
import io.netty.handler.codec.protobuf.ProtobufDecoder;
import io.netty.handler.codec.protobuf.ProtobufEncoder;
import io.netty.handler.codec.protobuf.ProtobufVarint32FrameDecoder;
import io.netty.handler.codec.protobuf.ProtobufVarint32LengthFieldPrepender;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import io.netty.handler.timeout.IdleStateHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Scope("prototype")
@Component
@RequiredArgsConstructor
public class TcpChannelInitializer extends ChannelInitializer<Channel> {

    private final ConfigProperties configProperties;

    private final TcpServerHandler tcpServerHandler;

    private final ProtobufDecoder protobufDecoder = new ProtobufDecoder(Command.getDefaultInstance());

    private final ProtobufVarint32LengthFieldPrepender protobufVarint32LengthFieldPrepender = new ProtobufVarint32LengthFieldPrepender();

    private final ProtobufEncoder protobufEncoder = new ProtobufEncoder();

    @Override
    protected void initChannel(Channel channel) {
        channel.pipeline()
//                .addLast(new LoggingHandler(LogLevel.TRACE))
                // 设置心跳检查
                .addLast(new IdleStateHandler(configProperties.getHeartbeat().getReadTimeout() / 1000, 0, 0, TimeUnit.SECONDS))
//                .addLast(new RawDataPrinterHandler())
                // 设置 protobuf 解码器
                .addLast(new ProtobufVarint32FrameDecoder())
                .addLast(protobufDecoder)
                .addLast(protobufVarint32LengthFieldPrepender)
                .addLast(protobufEncoder)
                .addLast(tcpServerHandler);
    }
}
