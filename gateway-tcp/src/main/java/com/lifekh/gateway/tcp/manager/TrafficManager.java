package com.lifekh.gateway.tcp.manager;

import com.lifekh.gateway.tcp.properties.ConfigProperties;
import io.netty.channel.socket.SocketChannel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class TrafficManager {

    private Map<String,Integer> authCountMap = new ConcurrentHashMap<>();


    @Autowired
    private ConfigProperties configProperties;


    public void addAuthCount(String key,int count) {
        Integer oldCount = authCountMap.get(key);
        if(oldCount == null) {
            authCountMap.put(key, count);
        } else {
            log.info("计数器:{},value:{} 累加1",key,oldCount);
            authCountMap.put(key, oldCount + count);
        }
    }

    public boolean isAuthCountLimit(String key) {
        Integer count = authCountMap.get(key);
        if(count == null) {
            return false;
        }
        int authLimit = configProperties.getMetrics().getAuthLimit();
        return count >= authLimit;
    }

    @PostConstruct
    public void init(){
        int authInterval =  configProperties.getMetrics().getAuthInterval();
        new Thread(()->{
            while(true){
                try {
                    Thread.sleep(authInterval);
                    authCountMap.clear();
                    log.info("计数器清零");
                } catch (Exception e) {
                    log.error("计数清楚异常",e);
                }
            }
        }).start();
    }

}
