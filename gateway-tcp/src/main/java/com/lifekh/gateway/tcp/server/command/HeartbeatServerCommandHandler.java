package com.lifekh.gateway.tcp.server.command;

import cn.hutool.json.JSONUtil;
import com.chaos.keep.alive.common.core.constant.CommandType;
import com.chaos.keep.alive.common.im.domain.JsonCommand;
import com.chaos.keep.alive.common.protobuf.Command;
import com.chaos.keep.alive.common.protobuf.Result;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Component
public class HeartbeatServerCommandHandler implements ServerCommandHandler {

    @Override
    public void handleCommand(JsonCommand jsonCommand, ChannelHandlerContext ctx) {
        JsonCommand newCommand = new JsonCommand();
        newCommand.setBizType(jsonCommand.getBizType());
        newCommand.setOperatorNo(jsonCommand.getOperatorNo());
        newCommand.setDeviceId(jsonCommand.getDeviceId());
        ctx.writeAndFlush(new TextWebSocketFrame(JSONUtil.toJsonStr(newCommand)));
    }

    @Override
    public void handleCommand(Command command, ChannelHandlerContext ctx) {
        Result result = Result.newBuilder()
                .setSuccess(true)
                .build();
        Command newCommand = Command.newBuilder()
                .setOperatorNo(command.getOperatorNo())
                .setDeviceId(command.getDeviceId())
                .setTimestamp(System.currentTimeMillis())
                .setBizType(command.getBizType())
                .setBody(result.toByteString()).build();
        ctx.writeAndFlush(newCommand);
    }

    @Override
    public int getType() {
        return CommandType.COMMAND_HEARTBEAT;
    }
}
