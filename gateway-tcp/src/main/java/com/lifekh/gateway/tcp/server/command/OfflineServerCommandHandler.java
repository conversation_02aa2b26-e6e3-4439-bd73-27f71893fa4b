package com.lifekh.gateway.tcp.server.command;

import com.chaos.keep.alive.common.core.constant.CommandType;
import com.chaos.keep.alive.common.im.domain.JsonCommand;
import com.chaos.keep.alive.common.protobuf.Command;
import com.lifekh.gateway.tcp.route.RouteTransport;
import io.netty.channel.ChannelHandlerContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class OfflineServerCommandHandler implements ServerCommandHandler {

    private final RouteTransport routeTransport;

    @Override
    public void handleCommand(JsonCommand jsonCommand, ChannelHandlerContext ctx) {
        int type = jsonCommand.getBizType();
        Command command = Command.newBuilder()
                .setOperatorNo(jsonCommand.getOperatorNo())
                .setDeviceId(jsonCommand.getDeviceId())
                .setBizType(type)
                .build();
        handleCommand(command, ctx);
    }

    @Override
    public void handleCommand(Command command, ChannelHandlerContext ctx) {
        log.info("客户端下线：{}", command.getOperatorNo() + ":" + command.getDeviceId());
        routeTransport.send(command, command.getOperatorNo());
    }

    @Override
    public int getType() {
        return CommandType.COMMAND_OFFLINE;
    }
}
