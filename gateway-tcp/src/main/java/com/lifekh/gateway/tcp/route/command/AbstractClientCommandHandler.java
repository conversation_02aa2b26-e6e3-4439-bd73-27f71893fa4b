package com.lifekh.gateway.tcp.route.command;

import com.chaos.keep.alive.common.core.exception.SystemException;
import com.chaos.keep.alive.common.protobuf.Command;
import com.lifekh.gateway.tcp.util.GatewayContext;
import io.netty.channel.ChannelHandlerContext;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
public abstract class AbstractClient<PERSON>ommand<PERSON><PERSON>ler implements ClientCommandHandler {

    @Autowired
    protected GatewayContext context;

    @Override
    public void handleCommand(Command command, ChannelHandlerContext ctx) {
        if (context.isWebSocket()) {
            handleJsonCommand(command, ctx);
        } else if (context.isTcp()) {
            handleProtoCommand(command, ctx);
        } else {
            throw new SystemException("不支持的协议");
        }
    }

    public abstract int getType();

    public abstract void handleProtoCommand(Command command, ChannelHandlerContext ctx);

    public abstract void handleJsonCommand(Command command, ChannelHandlerContext ctx);
}
