package com.lifekh.gateway.tcp.route.strategy;

import com.chaos.keep.alive.common.core.exception.SystemException;
import com.lifekh.gateway.tcp.route.RouteClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class RoundRobinRouteStrategy implements RouteStrategy {

    private final AtomicInteger index = new AtomicInteger();

    @Override
    public RouteClient routeServer(List<RouteClient> clients, String key) {
        if (clients.size() == 0) {
            log.info("没有可用的路由服务,key:{}",key);
            throw new SystemException("没有可用的路由服务");
        }
        int position = index.incrementAndGet() % clients.size();
        if (position < 0) {
            position = 0;
        }
        return clients.get(position);
    }
}
