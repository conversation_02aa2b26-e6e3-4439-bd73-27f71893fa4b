package com.lifekh.gateway.tcp.server.command;

import cn.hutool.json.JSONUtil;
import com.chaos.keep.alive.common.core.constant.CommandType;
import com.chaos.keep.alive.common.core.constant.MessageType;
import com.chaos.keep.alive.common.im.domain.JsonCommand;
import com.chaos.keep.alive.common.protobuf.Command;
import com.chaos.keep.alive.common.protobuf.Result;
import com.chaos.keep.alive.common.protobuf.RiderReportLocationRequest;
import com.lifekh.gateway.tcp.route.RouteTransport;
import io.netty.channel.ChannelHandlerContext;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@RequiredArgsConstructor
@Component
public class RiderReportLocationCommandHandler implements ServerCommandHandler{

    private final RouteTransport routeTransport;

    @Override
    public void handleCommand(JsonCommand jsonCommand, ChannelHandlerContext ctx) {

        RiderReportLocationRequest body  = JSONUtil.toBean(jsonCommand.getBody(), RiderReportLocationRequest.class);

        log.info("接收骑手上报位置operatorNo:{},riderId:{},lat:{},lon:{}",jsonCommand.getOperatorNo(),body.getRiderId(),body.getLatitude(),body.getLongitude());
        Command command = Command.newBuilder()
                .setBizType(jsonCommand.getBizType())
                .setOperatorNo(jsonCommand.getOperatorNo())
                .setDeviceId(jsonCommand.getDeviceId())
                .setBody(body.toByteString()).build();
        handleCommand(command, ctx);
    }

    @Override
    @SneakyThrows
    public void handleCommand(Command command, ChannelHandlerContext ctx) {
        try {

            RiderReportLocationRequest body = RiderReportLocationRequest.parseFrom(command.getBody());
            if("Delivery".equals(command.getAppId())&& (body.getRiderId() ==0) ){
                log.error("骑手上报位置riderId不能为空");
                throw new IllegalAccessException("骑手上报位置riderId不能为空");
            }

            log.info("发送骑手上报位置消息给route,operatorNo:{},deviceId:{},latitude:{},longitude:{}",command.getOperatorNo(),command.getDeviceId(), body.getLatitude(), body.getLongitude());

            routeTransport.send(command, command.getOperatorNo());
        }catch (Exception e){
            log.error(String.format("operatorNo:%s,deviceId:%s,发送上报位置消息给route失败",command.getOperatorNo(),command.getDeviceId()),e);
            Result result =  Result.newBuilder().setSuccess(false).setErrorMessage(e.getMessage()).build();
            Command response = Command.newBuilder()
                    .setAppSdkVersion(command.getAppSdkVersion())
                    .setMessageType(MessageType.MESSAGE_TYPE_PUSH)
                    .setBizType(command.getBizType())
                    .setTimestamp(command.getTimestamp())
                    .setOperatorNo(command.getOperatorNo())
                    .setDeviceId(command.getDeviceId())
                    .setAppId(command.getAppId())
                    .setBody(result.toByteString()).build();
            ctx.writeAndFlush(response);
        }
    }

    @Override
    public int getType() {
        return CommandType.COMMAND_WOWNOW_RIDER_REPORT_LOCATION;
    }
}
