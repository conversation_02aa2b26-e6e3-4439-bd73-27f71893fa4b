package com.lifekh.gateway.tcp.route.command;

import com.chaos.keep.alive.common.core.constant.CommandType;
import com.chaos.keep.alive.common.protobuf.Command;
import com.lifekh.gateway.tcp.route.RouteClient;
import com.lifekh.gateway.tcp.route.RouteTransport;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.socket.SocketChannel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class HeartbeatClientCommandHandler extends AbstractClientCommandHandler {

    private final RouteTransport routeTransport;

    @Override
    public void handleProtoCommand(Command command, ChannelHandlerContext ctx) {
        Optional<RouteClient> optional = routeTransport.get((SocketChannel) ctx.channel());
        optional.ifPresent(routeClient -> log.trace("收到[{}]的心跳响应", routeClient.getAddressInstance().getServerId()));
    }

    @Override
    public void handleJsonCommand(Command command, ChannelHandlerContext ctx) {
        handleProtoCommand(command, ctx);
    }

    @Override
    public int getType() {
        return CommandType.COMMAND_HEARTBEAT;
    }
}
