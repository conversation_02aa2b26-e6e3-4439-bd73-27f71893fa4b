package com.lifekh.gateway.tcp.server.command;

import com.chaos.keep.alive.common.im.domain.JsonCommand;
import com.chaos.keep.alive.common.protobuf.Command;
import io.netty.channel.ChannelHandlerContext;

/**
 * <AUTHOR>
 */
public interface ServerCommandHandler {

    void handleCommand(JsonCommand jsonCommand, ChannelHandlerContext ctx);

    void handleCommand(Command command, ChannelHandlerContext ctx);

    int getType();
}
