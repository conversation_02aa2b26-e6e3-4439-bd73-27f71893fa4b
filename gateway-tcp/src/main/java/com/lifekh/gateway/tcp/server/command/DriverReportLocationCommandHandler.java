package com.lifekh.gateway.tcp.server.command;

import cn.hutool.json.JSONUtil;
import com.chaos.keep.alive.common.core.constant.CommandType;
import com.chaos.keep.alive.common.core.constant.MessageType;
import com.chaos.keep.alive.common.im.domain.JsonCommand;
import com.chaos.keep.alive.common.protobuf.Command;
import com.chaos.keep.alive.common.protobuf.ReportLocationRequest;
import com.chaos.keep.alive.common.protobuf.Result;
import com.lifekh.gateway.tcp.client.ClientManager;
import com.lifekh.gateway.tcp.route.RouteTransport;
import com.lifekh.gateway.tcp.util.GatewayContext;
import io.netty.channel.ChannelHandlerContext;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@RequiredArgsConstructor
@Component
public class DriverReportLocationCommandHandler implements ServerCommandHandler{

    private final RouteTransport routeTransport;

    private final ClientManager clientManager;

    private final GatewayContext context;

    @Override
    public void handleCommand(JsonCommand jsonCommand, ChannelHandlerContext ctx) {

        ReportLocationRequest body  = JSONUtil.toBean(jsonCommand.getBody(), ReportLocationRequest.class);

        log.info("接受到上报位置operatorNo:{},lat:{}:lon:{},speed:{},direction:{}",jsonCommand.getOperatorNo(),body.getLatitude(),body.getLongitude(),body.getSpeed(),body.getDirection() );
        Command command = Command.newBuilder()
                .setBizType(jsonCommand.getBizType())
                .setOperatorNo(jsonCommand.getOperatorNo())
                .setDeviceId(jsonCommand.getDeviceId())
                .setBody(body.toByteString()).build();
        handleCommand(command, ctx);
    }

    @Override
    @SneakyThrows
    public void handleCommand(Command command, ChannelHandlerContext ctx) {
        try {

            ReportLocationRequest body = ReportLocationRequest.parseFrom(command.getBody());
            if("GoNowDriver".equals(command.getAppId())&& (body.getDriverId() ==0) ){
                log.error("司机上报位置driverId为空");
                throw new IllegalAccessException("司机上报位置driverId为空");
            }

            log.debug("发送上报位置消息给route,operatorNo:{},deviceId:{},latitude:{},longitude:{}",command.getOperatorNo(),command.getDeviceId(), body.getLatitude(), body.getLongitude());

            routeTransport.send(command, command.getOperatorNo());
        }catch (Exception e){
            log.error(String.format("operatorNo:%s,deviceId:%s,发送上报位置消息给route失败",command.getOperatorNo(),command.getDeviceId()),e);
            Result result =  Result.newBuilder().setSuccess(false).setErrorMessage(e.getMessage()).build();
            Command response = Command.newBuilder()
                    .setAppSdkVersion(command.getAppSdkVersion())
                    .setMessageType(MessageType.MESSAGE_TYPE_PUSH)
                    .setBizType(command.getBizType())
                    .setTimestamp(command.getTimestamp())
                    .setOperatorNo(command.getOperatorNo())
                    .setDeviceId(command.getDeviceId())
                    .setAppId(command.getAppId())
                    .setBody(result.toByteString()).build();
            ctx.writeAndFlush(response);
        }
    }

    @Override
    public int getType() {
        return CommandType.COMMAND_GONOW_DRIVER_REPORT_LOCATION;
    }
}
