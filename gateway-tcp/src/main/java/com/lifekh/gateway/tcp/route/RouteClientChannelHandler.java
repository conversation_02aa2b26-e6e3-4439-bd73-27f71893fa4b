package com.lifekh.gateway.tcp.route;

import com.chaos.keep.alive.common.core.util.ChannelUtils;
import com.chaos.keep.alive.common.protobuf.Command;
import com.lifekh.gateway.tcp.route.command.ClientCommandHandler;
import com.lifekh.gateway.tcp.route.command.ClientCommandHandlerFactory;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.channel.socket.SocketChannel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.net.UnknownHostException;

@Slf4j
@Component
@RequiredArgsConstructor
@ChannelHandler.Sharable
public class RouteClientChannelHandler extends SimpleChannelInboundHandler<Command> {

    private final RouteTransport routeTransport;

    private final ClientCommandHandlerFactory clientCommandHandlerFactory;

    /**
     * 路由服务连接被断开，删除客户端
     */
    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        SocketChannel routeChannel = (SocketChannel) ctx.channel();
        RouteClient routeClient = routeTransport.remove(ChannelUtils.getChannelId(routeChannel));
        routeClient.close();
        log.info("路由服务[{}]的连接已断开", routeClient.getAddressInstance().getServerId());
    }

    @Override
    public void channelRead0(ChannelHandlerContext ctx, Command command) {
        try {
            int type = command.getBizType();
            ClientCommandHandler commandHandler = clientCommandHandlerFactory.getCommandHandler(type);
            commandHandler.handleCommand(command, ctx);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws UnknownHostException {
        if (cause instanceof UnknownHostException) {
            throw (UnknownHostException) cause;
        }
        log.error(cause.getMessage(), cause);
    }
}
