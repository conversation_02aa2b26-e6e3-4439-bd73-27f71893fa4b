package com.lifekh.gateway.tcp;

import com.lifekh.gateway.tcp.server.GatewayServer;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@RequiredArgsConstructor
@EnableScheduling
public class GatewayTcpApplication implements ApplicationListener<ApplicationReadyEvent> {

    private final GatewayServer gatewayServer;

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(GatewayTcpApplication.class);
        application.setWebApplicationType(WebApplicationType.SERVLET);
        application.run(args);
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent applicationReadyEvent) {
        gatewayServer.start();
    }


}
