package com.lifekh.gateway.tcp.server.tcp;

import cn.hutool.core.util.HexUtil;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class RawDataPrinterHandler extends ByteToMessageDecoder {
    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {
        // 打印当前缓冲区的可读字节
        if (in.isReadable()) {
            byte[] bytes = new byte[in.readableBytes()];
            in.readBytes(bytes);
            log.info("Raw data: " + HexUtil.encodeHexStr(bytes));
        }
        // 将数据传递给后续 Handler
        out.add(in.retain());
    }
}
