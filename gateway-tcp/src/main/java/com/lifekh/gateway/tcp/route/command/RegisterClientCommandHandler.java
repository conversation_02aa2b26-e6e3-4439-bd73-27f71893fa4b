package com.lifekh.gateway.tcp.route.command;

import com.chaos.keep.alive.common.core.constant.CommandType;
import com.chaos.keep.alive.common.protobuf.Command;
import com.lifekh.gateway.tcp.properties.ConfigProperties;
import com.lifekh.gateway.tcp.route.RouteClient;
import com.lifekh.gateway.tcp.route.RouteTransport;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.socket.SocketChannel;
import io.netty.util.concurrent.ScheduledFuture;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 向路由服务注册成功后的处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RegisterClientCommandHandler extends AbstractClientCommandHandler {

    private final RouteTransport routeTransport;

    private final ConfigProperties configProperties;

    @SneakyThrows
    @Override
    public void handleProtoCommand(Command command, ChannelHandlerContext ctx) {
        Optional<RouteClient> optional = routeTransport.get((SocketChannel) ctx.channel());
        optional.ifPresent(routeClient -> {
            log.info("向路由服务[{}]注册成功...", routeClient.getAddressInstance().getServerId());
            // 设置ChannelHandlerContext
            routeClient.setContext(ctx);
            // 开启心跳调度
            ScheduledFuture<?> scheduledFuture = routeClient.getContext().executor().scheduleAtFixedRate(() -> {
                if (routeClient.getChannel().isActive()) {
                    log.trace("发送心跳给[{}]", routeClient.getAddressInstance().getServerId());
                    Command newCommand = Command.newBuilder().setBizType(CommandType.COMMAND_HEARTBEAT).build();
                    ctx.writeAndFlush(newCommand);
                }
            }, 0, configProperties.getHeartbeat().getHeartbeatInterval(), TimeUnit.MILLISECONDS);
            routeClient.setScheduledFuture(scheduledFuture);
        });
    }

    @Override
    public void handleJsonCommand(Command command, ChannelHandlerContext ctx) {
        handleProtoCommand(command, ctx);
    }

    @Override
    public int getType() {
        return CommandType.COMMAND_REGISTER;
    }
}
