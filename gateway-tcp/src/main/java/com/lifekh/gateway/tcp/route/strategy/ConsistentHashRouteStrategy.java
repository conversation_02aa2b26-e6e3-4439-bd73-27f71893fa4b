package com.lifekh.gateway.tcp.route.strategy;

import com.chaos.keep.alive.common.core.exception.SystemException;
import com.lifekh.gateway.tcp.route.RouteClient;
import com.lifekh.gateway.tcp.route.strategy.consistent.ConsistentHashRoute;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ConsistentHashRouteStrategy implements RouteStrategy {

    @Override
    public RouteClient routeServer(List<RouteClient> clients, String key) {
        if (clients.size() == 0) {
            log.info("没有可用的路由服务 key :{}",key);
            throw new SystemException("没有可用的路由服务");
        }
        ConsistentHashRoute<RouteClient> consistentHashRoute = new ConsistentHashRoute<>(clients, 128);
        return consistentHashRoute.routeNode(String.valueOf(key));
    }
}
