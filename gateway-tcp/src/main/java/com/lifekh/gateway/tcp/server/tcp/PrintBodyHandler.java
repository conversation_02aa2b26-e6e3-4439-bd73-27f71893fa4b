package com.lifekh.gateway.tcp.server.tcp;

import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PrintBodyHandler extends ChannelInboundHandlerAdapter{
    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        try {
            if (msg instanceof byte[]) {
                byte[] bytes = (byte[]) msg;
                log.debug("消息格式:{},解码前先打印消息:{}",msg.getClass(), bytes);
            } else if (msg instanceof String) {
                log.debug("消息格式:{},解码前先打印消息:{}",msg.getClass(), msg);
            } else {
                log.debug("消息格式:{},解码前先打印消息:{}", msg.getClass(), msg);
            }
        }catch (Exception e){
            log.debug("打印消息异常",e);
        }
        super.channelRead(ctx, msg);
    }
}
