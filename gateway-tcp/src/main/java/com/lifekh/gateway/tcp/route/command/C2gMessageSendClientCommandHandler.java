package com.lifekh.gateway.tcp.route.command;

import cn.hutool.json.JSONUtil;
import com.chaos.keep.alive.common.core.domain.MessageSendJsonResponse;
import com.chaos.keep.alive.common.im.constant.CommandType;
import com.chaos.keep.alive.common.im.constant.MessageType;
import com.chaos.keep.alive.common.im.domain.JsonCommand;
import com.chaos.keep.alive.common.im.domain.MessageJsonPush;
import com.chaos.keep.alive.common.protobuf.Command;
import com.chaos.keep.alive.common.protobuf.ImMessagePush;
import com.chaos.keep.alive.common.protobuf.MessageSendResponse;
import com.lifekh.gateway.tcp.client.ClientManager;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class C2gMessageSendClientCommandHandler extends AbstractClientCommandHandler {

    private final ClientManager clientManager;

    @Override
    public void handleProtoCommand(Command command, ChannelHandlerContext ctx) {
        SocketChannel clientChannel = clientManager.getChannel(command.getOperatorNo(), command.getDeviceId());
        clientChannel.writeAndFlush(command);
    }

    @SneakyThrows
    @Override
    public void handleJsonCommand(Command command, ChannelHandlerContext ctx) {
        SocketChannel clientChannel = clientManager.getChannel(command.getOperatorNo(), command.getDeviceId());
        JsonCommand jsonCommand = null;
        log.info("messageType:{}",command.getMessageType());
        if (command.getMessageType() == MessageType.MESSAGE_TYPE_PUSH) {

            ImMessagePush imMessagePush = ImMessagePush.parseFrom(command.getBody());

            MessageJsonPush body = new MessageJsonPush();
            body.setMessageId(imMessagePush.getMessageId());
            body.setContent(imMessagePush.getContent());
            body.setCategory(imMessagePush.getCategory());
            body.setChatId(imMessagePush.getChatId());
            body.setChatType(imMessagePush.getChatType());
            body.setFromOperatorNo(imMessagePush.getFromOperatorNo());
            body.setToOperatorNo(imMessagePush.getToOperatorNo());


            jsonCommand = JsonCommand.convert(command);

            jsonCommand.setBody(JSONUtil.toJsonStr(body));

            log.info("发送群聊消息给toOperatorNo:{} content:{}", body.getToOperatorNo(), body.getContent());
        } else if (command.getMessageType() == MessageType.MESSAGE_TYPE_SERVER_ACK) {
            MessageSendResponse response = MessageSendResponse.parseFrom(command.getBody());

            MessageSendJsonResponse body = new MessageSendJsonResponse();
            body.setMessageId(response.getMessageId());
            body.setChatId(response.getChatId());
            body.setChatType(response.getChatType());
            body.setSequence(response.getSequence());
            body.setTimestamp(response.getTimestamp());
            body.setFromOperatorNo(response.getFromOperatorNo());
            body.setToOperatorNo(response.getToOperatorNo());
            jsonCommand = JsonCommand.convert(command);
            jsonCommand.setBody(JSONUtil.toJsonStr(body));
            log.info("发送群聊消息响应toOperatorNo:{} messageId:{}", body.getToOperatorNo(), body.getMessageId());
        }
        clientChannel.writeAndFlush(new TextWebSocketFrame(JSONUtil.toJsonStr(jsonCommand)));
    }

    @Override
    public int getType() {
        return CommandType.COMMAND_IM_C2G_MESSAGE_SEND;
    }
}
