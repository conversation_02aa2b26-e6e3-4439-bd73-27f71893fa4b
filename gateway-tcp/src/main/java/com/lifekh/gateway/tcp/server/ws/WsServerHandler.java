package com.lifekh.gateway.tcp.server.ws;

import cn.hutool.json.JSONUtil;
import com.chaos.keep.alive.common.core.constant.CommandType;
import com.chaos.keep.alive.common.im.domain.JsonCommand;
import com.lifekh.gateway.tcp.client.ClientManager;
import com.lifekh.gateway.tcp.server.command.ServerCommandHandler;
import com.lifekh.gateway.tcp.server.command.ServerCommandHandlerFactory;
import com.lifekh.gateway.tcp.util.GatewayContext;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ChannelHandler.Sharable
public class WsServerHandler extends SimpleChannelInboundHandler<TextWebSocketFrame> {

    private final ServerCommandHandlerFactory serverCommandHandlerFactory;

    private final ClientManager clientManager;

    private final GatewayContext context;

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        log.info("ws客户端连接已建立");
        super.channelActive(ctx);
    }

    /**
     * 客户端连接断开
     */
    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        log.info("ws客户端连接断开");
        offline(ctx);
    }

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, TextWebSocketFrame frame) {
        String jsonStr = frame.text();
        JsonCommand jsonCommand = JSONUtil.toBean(jsonStr, JsonCommand.class);
        int type = jsonCommand.getBizType();
        ServerCommandHandler commandHandler = serverCommandHandlerFactory.getCommandHandler(type);
        commandHandler.handleCommand(jsonCommand, ctx);
    }

    /**
     * 客户端心跳检查超时
     */
    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) {
        if (evt instanceof IdleStateEvent) {
            IdleStateEvent idleStateEvent = (IdleStateEvent) evt;
            if (idleStateEvent.state() == IdleState.READER_IDLE) {
                SocketChannel clientChannel = (SocketChannel) ctx.channel();
                Optional<ClientManager.ClientInstance> optional = clientManager.getClientId(clientChannel);
                optional.ifPresent(clientInstance -> log.info("ws读心跳超时:{}", clientInstance.clientId()));
                offline(ctx);
            }
        }
    }

    private void offline(ChannelHandlerContext ctx) {
        SocketChannel socketChannel = (SocketChannel) ctx.channel();
        Optional<ClientManager.ClientInstance> optional = clientManager.removeChannel(socketChannel);
        optional.ifPresent(clientInstance -> {
            context.offline();
            ServerCommandHandler commandHandler = serverCommandHandlerFactory.getCommandHandler(CommandType.COMMAND_OFFLINE);
            JsonCommand jsonCommand = new JsonCommand();
            jsonCommand.setOperatorNo(clientInstance.getOperatorNo());
            jsonCommand.setDeviceId(clientInstance.getDeviceId());
            jsonCommand.setBizType(CommandType.COMMAND_OFFLINE);
            commandHandler.handleCommand(jsonCommand, ctx);
        });
        ctx.close();
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        log.error(cause.getMessage(), cause);
    }
}
