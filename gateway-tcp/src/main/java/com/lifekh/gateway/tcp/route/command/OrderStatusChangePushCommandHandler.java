package com.lifekh.gateway.tcp.route.command;

import cn.hutool.json.JSONUtil;
import com.chaos.keep.alive.common.core.constant.CommandType;
import com.chaos.keep.alive.common.core.domain.MessageJsonPush;
import com.chaos.keep.alive.common.im.domain.JsonCommand;
import com.chaos.keep.alive.common.protobuf.Command;
import com.chaos.keep.alive.common.protobuf.MessagePush;
import com.lifekh.gateway.tcp.client.ClientManager;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderStatusChangePushCommandHandler extends AbstractClientCommandHandler {

    private final ClientManager clientManager;

    @Override
    public void handleProtoCommand(Command command, ChannelHandlerContext ctx) {
        SocketChannel clientChannel = clientManager.getChannel(command.getOperatorNo(), command.getDeviceId());
        try {
            MessagePush messagePush = MessagePush.parseFrom(command.getBody());
            log.info("send order status change message to clientChannel:{},operatorNo:{},deviceId:{},body:{}",clientChannel,command.getOperatorNo(),command.getDeviceId(), messagePush.toByteString() );
        }catch (Exception e){

        }
        clientChannel.writeAndFlush(command);
    }

    @SneakyThrows
    @Override
    public void handleJsonCommand(Command command, ChannelHandlerContext ctx) {
        MessagePush body = MessagePush.parseFrom(command.getBody());

        JsonCommand jsonCommand = JsonCommand.convert(command);
        MessageJsonPush data = new MessageJsonPush();
        data.setContent(body.getContent());
        jsonCommand.setBody(JSONUtil.toJsonStr(data));
        SocketChannel clientChannel = clientManager.getChannel(command.getOperatorNo(), command.getDeviceId());
        if (Objects.nonNull(clientChannel)) {
            clientChannel.writeAndFlush(new TextWebSocketFrame(JSONUtil.toJsonStr(jsonCommand)));
        }
    }

    @Override
    public int getType() {
        return CommandType.COMMAND_GONOW_ORDER_STATUS_CHANGE;
    }
}
