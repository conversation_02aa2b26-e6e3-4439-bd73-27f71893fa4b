package com.lifekh.gateway.tcp.util;


import com.chaos.keep.alive.common.core.domain.JsonResult;
import com.chaos.keep.alive.common.protobuf.Result;

/**
 * <AUTHOR>
 */
public class ResultConverter {

    public static <T> JsonResult<T> convert(Result result) {
        JsonResult<T> jsonResult = new JsonResult<>();
        jsonResult.setSuccess(result.getSuccess());
        jsonResult.setErrorCode(result.getErrorCode());
        jsonResult.setErrorMessage(result.getErrorMessage());
        return jsonResult;
    }
}
