package com.lifekh.gateway.tcp.client;

import cn.hutool.json.JSONUtil;
import com.chaos.keep.alive.common.core.util.ChannelUtils;
import io.netty.channel.socket.SocketChannel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 管理与客户端的长连接
 */
@Slf4j
@Component
public class ClientManager {
    private final ConcurrentHashMap<String, SocketChannel> clients = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<String, ClientInstance> channelId2Client = new ConcurrentHashMap<>();


    public String printChannelId2Client() {
        String json = JSONUtil.toJsonStr(channelId2Client);
        log.info("channelId2Client info :{}", json);
        return json;
    }

    @Scheduled(fixedRate = 10, initialDelay = 1, timeUnit = TimeUnit.MINUTES)
    public void clientStat() {
        log.info("[ClientStat] Clients: {}, ChannelId2Client: {}", clients.size(), channelId2Client.size());
    }

    public void addChannel(String operatorNo, String deviceId, SocketChannel socketChannel) {
        ClientInstance clientInstance = new ClientInstance(operatorNo, deviceId);
        channelId2Client.put(ChannelUtils.getChannelId(socketChannel), clientInstance);
        log.info("Channel added to client manager with key {} -> {}", clientInstance.clientId(), socketChannel);
        clients.put(clientInstance.clientId(), socketChannel);
    }

    public Optional<ClientInstance> removeChannel(SocketChannel socketChannel) {
        String channelId = ChannelUtils.getChannelId(socketChannel);
        log.info("Channel removed from client manager with key {} -> {}", channelId, socketChannel);
        if (channelId2Client.containsKey(channelId)) {
            ClientInstance clientInstance = channelId2Client.get(channelId);
            clients.remove(clientInstance.clientId());
            socketChannel.close();
            return Optional.ofNullable(channelId2Client.remove(channelId));
        }
        return Optional.empty();
    }

    public Optional<ClientInstance> getClientId(SocketChannel socketChannel) {
        return Optional.ofNullable(channelId2Client.get(ChannelUtils.getChannelId(socketChannel)));
    }

    public SocketChannel getChannel(String operatorNo, String deviceId) {
        return clients.get(clientId(operatorNo, deviceId));
    }

    private String clientId(String operatorNo, String deviceId) {
        return operatorNo + ":" + deviceId;
    }

    public boolean isConnected(String clientId) {
        return clients.containsKey(clientId);
    }

    @Getter
    @Setter
    @RequiredArgsConstructor
    public static class ClientInstance {
        private final String operatorNo;

        private final String deviceId;

        public String clientId() {
            return operatorNo + ":" + deviceId;
        }
    }
}
