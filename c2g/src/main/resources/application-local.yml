go:
    mvc:
        responseFilter: /actuator/prometheus
dubbo:
    application:
        shutdown:
            wait: 30000       # 单位 ms，等待处理中请求完成的时间（必须 > spring.lifecycle.timeout）
            hook: true
        shutwait: "false"

        lifecycle:
            register-shutdown-hook: true
        name: c2g
        qos-port: 33333
    consumer:
        group: chaos
    protocol:
        port: 20883
        destroyWait: 30000
    provider:
        group: chaos
    registry:
#        address: zookeeper://localhost:2181
        address: zookeeper://*************:2181
        unregister-on-shutdown: true
#        address: zookeeper://zk-dubbo-0.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181?backup=zk-dubbo-1.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181,zk-dubbo-2.hs-zk-dubbo.lifekh-tool-sit.svc.cluster.local:2181
        check: false
mybatis-plus:
    configuration:
        log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    global-config:
        banner: false
        db-config:
            id-type: auto
server:
    port: 8096
    servlet:
        context-path: /c2g
    shutdown: graceful
spring:
    lifecycle:
        timeout-per-shutdown-phase: 60s
    application:
        name: c2g
    data:
        mongodb:
            database: REPORT_BEHAVIOR
            host: ************
            password: report_behavior_2020
            port: 27017
            username: report_behavior
    datasource:
        asyncInit: true
        driver-class-name: com.mysql.cj.jdbc.Driver
        filters: stat
        initialSize: 1
        maxActive: 3
        maxOpenPreparedStatements: 20
        maxWait: 60000
        minEvictableIdleTimeMillis: 300000
        minIdle: 1
        password: wownowim_sit_2025
        poolPreparedStatements: true
        testOnBorrow: false
        testOnReturn: false
        testWhileIdle: true
        timeBetweenEvictionRunsMillis: 60000
        url: ***************************************************************************************************************************************************************
        username: wownowim_sit
    kafka:
        consumer:
            auto-offset-reset: latest
            bootstrap-servers: ************:39092,************:39092,************:39092
            enable-auto-commit: false
            group-id: c2g
            key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
            value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
        listener:
            ack-mode: manual_immediate
            type: batch
        producer:
            bootstrap-servers: ************:39092,************:39092,************:39092
            key-serializer: org.apache.kafka.common.serialization.StringSerializer
            value-serializer: org.apache.kafka.common.serialization.StringSerializer
    main:
        allow-bean-definition-overriding: true
        banner-mode: 'off'
    redis:
        host: *************
#        host: localhost
        port: 6379
logging:
    level:
        org.apache.dubbo: DEBUG
        org.springframework.boot.web.embedded.tomcat: DEBUG
        org.springframework.boot: DEBUG