package com.chaos.im.c2g.group.domain;

import com.outstanding.framework.core.BaseEnum;

public enum GroupApplyReadStatusEnum implements BaseEnum<GroupApplyReadStatusEnum, Integer> {
    READ(10, "已读"),
    UNREAD(11, "未读");

    GroupApplyReadStatusEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    private Integer code;
    private String message;

    public Integer getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }

}
