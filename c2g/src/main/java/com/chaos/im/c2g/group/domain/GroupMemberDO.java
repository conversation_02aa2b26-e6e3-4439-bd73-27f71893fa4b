package com.chaos.im.c2g.group.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chaos.keep.alive.common.persistent.domain.BaseDO;
import lombok.Getter;
import lombok.Setter;


/**
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("group_member")
public class GroupMemberDO extends BaseDO {

    /**
     * 群组id
     */
    private Long groupId;

    /**
     * 账户id
     */
    private String memberId;
}