package com.chaos.im.c2g.group.domain;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class HandleApplyReqDTO implements Serializable {
    private static final long serialVersionUID = 4226391255288424636L;

    /**
     * 申请单编号
     */
    @NotBlank(message = "申请单编号不能为空")
    private Long applyId;

    /**
     * 处理结果：10-已解决、11-未解决
     */
    @NotNull(message = "处理结果不能为空")
    private Integer handleResult;

    /**
     * 客服操作员编号
     */
    @NotBlank(message = "操作员编号不能为空")
    private String operatorNo;

    /**
     * 备注
     */
    private String remark;
}
