package com.chaos.im.c2g.group.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chaos.keep.alive.common.persistent.domain.BaseDO;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("c2g_message_ack")
public class C2gMessageAckDO extends BaseDO {

    private Long chatId;

    private String memberId;

    private String deviceId;

    private Long lastAckMessageId;

    private Long sequence;
}

