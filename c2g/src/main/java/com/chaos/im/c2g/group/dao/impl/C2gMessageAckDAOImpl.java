package com.chaos.im.c2g.group.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chaos.im.c2g.group.dao.C2gMessageAckDAO;
import com.chaos.im.c2g.group.domain.C2gMessageAckDO;
import com.chaos.im.c2g.group.mapper.C2gMessageAckMapper;
import com.chaos.keep.alive.common.web.dao.MybatisPlusDAOImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class C2gMessageAckDAOImpl extends MybatisPlusDAOImpl<C2gMessageAckMapper, C2gMessageAckDO> implements C2gMessageAckDAO {

    @Override
    public List<C2gMessageAckDO> listByMemberId(Long memberId) {
        LambdaQueryWrapper<C2gMessageAckDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(C2gMessageAckDO::getMemberId, memberId);
        return mapper.selectList(queryWrapper);
    }

    @Override
    public void updateAck(Long chatId, String memberId, String deviceId, Long messageId,Long sequence) {
        LambdaUpdateWrapper<C2gMessageAckDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(C2gMessageAckDO::getLastAckMessageId, messageId);
        updateWrapper.set(C2gMessageAckDO::getSequence,sequence);
        updateWrapper.eq(C2gMessageAckDO::getChatId, chatId);
        updateWrapper.eq(C2gMessageAckDO::getMemberId, memberId);
        mapper.update(null, updateWrapper);
    }

    @Override
    public long count(Long chatId, String memberId, String deviceId) {
        LambdaQueryWrapper<C2gMessageAckDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(C2gMessageAckDO::getChatId, chatId);
        queryWrapper.eq(C2gMessageAckDO::getMemberId, memberId);
        queryWrapper.eq(C2gMessageAckDO::getDeviceId, deviceId);
        return mapper.selectCount(queryWrapper);
    }

    @Override
    public Long getLastAckMessageId(Long chatId, String memberId, String deviceId) {
        LambdaQueryWrapper<C2gMessageAckDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(C2gMessageAckDO::getChatId, chatId);
        queryWrapper.eq(C2gMessageAckDO::getMemberId, memberId);
        queryWrapper.eq(C2gMessageAckDO::getDeviceId, deviceId);
        C2gMessageAckDO c2gMessageAckDO= mapper.selectOne(queryWrapper);
        if(c2gMessageAckDO!=null) {
            return c2gMessageAckDO.getLastAckMessageId();
        }else{
            return  null;
        }
    }
}
