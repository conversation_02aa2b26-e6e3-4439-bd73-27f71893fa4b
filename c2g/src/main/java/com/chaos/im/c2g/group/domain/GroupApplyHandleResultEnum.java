package com.chaos.im.c2g.group.domain;

import com.outstanding.framework.core.BaseEnum;

public enum GroupApplyHandleResultEnum implements BaseEnum<GroupApplyHandleResultEnum, Integer> {
    RESOLVED(10, "已解决"),
    UNRESOLVED(11, "未解决");

    GroupApplyHandleResultEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    private Integer code;
    private String message;

    public Integer getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }

    public static GroupApplyHandleResultEnum getByCode(Integer code) {
        if (code != null) {
            for (GroupApplyHandleResultEnum status : GroupApplyHandleResultEnum.values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
        }
        return null;
    }
}
