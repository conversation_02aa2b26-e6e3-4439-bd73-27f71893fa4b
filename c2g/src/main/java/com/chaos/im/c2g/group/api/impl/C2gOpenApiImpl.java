package com.chaos.im.c2g.group.api.impl;

import com.chaos.im.c2g.group.api.domain.FetchQuery;
import com.chaos.im.c2g.group.api.domain.GroupMemberQuery;
import com.chaos.im.c2g.group.domain.GroupMemberVO;
import com.chaos.im.c2g.group.service.GroupMemberService;
import com.chaos.keep.alive.common.core.util.BeanCopierUtils;
import com.outstanding.framework.core.ResponseDTO;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import com.chaos.im.c2g.group.api.C2gOpenApi;
import com.chaos.im.c2g.group.api.domain.MessageSendReq;
import com.chaos.im.c2g.group.service.MessageSendService;

import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;

@AllArgsConstructor
@DubboService(version = "1.0.0", interfaceClass = C2gOpenApi.class)
public class C2gOpenApiImpl implements C2gOpenApi{

    @Autowired
    private MessageSendService messageSendService;

    @Autowired
    private GroupMemberService groupMemberService;

    @Override
    public void sendMessage(MessageSendReq req) {
        messageSendService.sendMessage(req);
    }



    @Override
    public ResponseDTO<?> fetch(@RequestBody FetchQuery fetchQuery) {
        return ResponseDTO.creatDTO(messageSendService.fetch(
                fetchQuery.getChatId(),
                fetchQuery.getOperatorNo(),
                fetchQuery.getDeviceId(),
                fetchQuery.getStartMessageId(),
                fetchQuery.getStopMessageId(),
                fetchQuery.getPageSize(),fetchQuery.getCurrent(),fetchQuery.getSort()));
    }

    @Override
    public ResponseDTO<?> listGroupMember(@RequestBody GroupMemberQuery query) {
        return ResponseDTO.creatDTO(BeanCopierUtils.convert(groupMemberService.listGroupMember(query), GroupMemberVO.class));
    }
    

    
}
