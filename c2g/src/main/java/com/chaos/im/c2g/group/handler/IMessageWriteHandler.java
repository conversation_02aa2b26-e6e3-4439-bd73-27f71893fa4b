package com.chaos.im.c2g.group.handler;

import com.chaos.im.c2g.group.domain.C2gMessageBO;
import org.apache.dubbo.common.extension.SPI;

@SPI
public interface IMessageWriteHandler {

    void setNextHandler(IMessageWriteHandler next);

     void handle(Long chatId, C2gMessageBO c2gMessageBO) ;

     boolean isSupport(Long chatId,C2gMessageBO c2gMessageBO);

     void handleRequest(Long chatId, C2gMessageBO c2gMessageBO);
}
