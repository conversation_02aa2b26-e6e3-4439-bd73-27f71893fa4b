package com.chaos.im.c2g.group.handler;

import com.alibaba.fastjson.JSONObject;
import com.chaos.im.c2g.group.domain.GroupDO;
import com.chaos.im.c2g.group.service.GroupMemberService;
import com.chaos.im.c2g.group.service.GroupService;
import com.chaos.im.chat.api.ChatApi;
import com.chaos.keep.alive.common.im.domain.MessageJsonPush;
import com.chaos.keep.alive.common.im.domain.MessageSendJsonRequest;
import com.chaos.keep.alive.common.web.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;

import java.util.List;
import java.util.Objects;

import static com.chaos.im.c2g.group.service.impl.GroupMemberServiceImpl.GROUP_KEY_PREFIX;

@Slf4j
public class MessageSendPushHandler extends MessageSendHandler{

    private ChatApi chatApi;
    private RedisTemplate<String, Object> redisTemplate;
    private GroupService groupService;
    private KafkaTemplate<String, String> kafkaTemplate;
    private GroupMemberService groupMemberService;
    private String TOPIC_MESSAGE_PUSH;
    private volatile boolean initialized = false;

    public MessageSendPushHandler() {
        // 延迟初始化，在第一次使用时初始化依赖
    }

    private void initDependencies() {
        if (!initialized && SpringContextUtils.getApplicationContext() != null) {
            synchronized (this) {
                if (!initialized) {
                    try {
                        this.chatApi = SpringContextUtils.getBean("chatApi", ChatApi.class);
                        this.redisTemplate = SpringContextUtils.getBean("redisTemplate", RedisTemplate.class);
                        this.groupService = SpringContextUtils.getBean(GroupService.class);
                        this.kafkaTemplate = SpringContextUtils.getBean("kafkaTemplate", KafkaTemplate.class);
                        this.groupMemberService = SpringContextUtils.getBean(GroupMemberService.class);

                        // 获取配置值
                        String[] activeProfiles = SpringContextUtils.getApplicationContext().getEnvironment().getActiveProfiles();
                        String activeProfile = activeProfiles.length > 0 ? activeProfiles[0] : "local";
                        this.TOPIC_MESSAGE_PUSH = "topic-message-push-" + activeProfile;

                        this.initialized = true;
                        log.info("MessageSendPushHandler dependencies initialized successfully");
                    } catch (Exception e) {
                        this.TOPIC_MESSAGE_PUSH = "topic-message-push-local";
                        log.error("Failed to initialize dependencies", e);
                        throw new RuntimeException("Failed to initialize MessageSendPushHandler dependencies", e);
                    }
                }
            }
        }
    }
    
    

    @Override
    public boolean isSupport(Long chatId, List<MessageSendJsonRequest> request) {
        return true;
    }

    @Override
    public void handleRequest(Long chatId, List<MessageSendJsonRequest> requests) {
        initDependencies();
        String groupId = chatApi.getPeerIdByChatId(chatId);
        List<?> memberIds = getMemberIds(chatId, groupId);
        GroupDO groupDO = groupService.getById(groupId);
        if (groupDO != null) {
            for (MessageSendJsonRequest request : requests) {
                sendMessagePush(request, (List<String>) memberIds, Long.parseLong(groupId));
            }
        } else {
            log.info("group had been deleted,groupId:{}", groupId);
        }
    }

    public void sendMessagePush(MessageSendJsonRequest request, List<String> memberIds, Long groupId) {
        for (String memberId : memberIds) {
            if (!Objects.equals(memberId, request.getFromOperatorNo())) {
                sendMessagePush(request, memberId, groupId);
            }
        }
    }

    public void sendMessagePush(MessageSendJsonRequest request, String memberId, Long groupId) {
        initDependencies();
        log.debug(request.toJsonStr());
        MessageJsonPush message = new MessageJsonPush();
        message.setMessageId(request.getMessageId());
        message.setFromOperatorNo(request.getFromOperatorNo());
        message.setToOperatorNo(memberId);
        message.setGroupId(groupId);
        message.setChatId(request.getChatId());
        message.setChatType(request.getChatType());
        message.setContent(request.getContent());
        message.setCategory(request.getCategory());
        message.setSequence(request.getSequence());
        message.setTimestamp(System.currentTimeMillis());
        kafkaTemplate.send(TOPIC_MESSAGE_PUSH, JSONObject.toJSONString(message));
    }

    private List<?> getMemberIds(Long chatId, String groupId) {
        initDependencies();
        List<?> memberIds = redisTemplate.opsForList().range(GROUP_KEY_PREFIX + "::" + chatId, 0, -1);
        log.info("get memberIds from redis by chatId :{}", memberIds);
        log.info("根据chatId:{} 获取groupId:{}", chatId, groupId);
        if (CollectionUtils.isEmpty(memberIds) && StringUtils.isNotEmpty(groupId)) {
            memberIds = groupMemberService.getByGroupId(Long.parseLong(groupId));
            log.info("get memberIds:{} from db by groupId : {}", memberIds, groupId);
        }
        return memberIds;
    }
}
