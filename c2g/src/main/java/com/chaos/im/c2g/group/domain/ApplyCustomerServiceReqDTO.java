package com.chaos.im.c2g.group.domain;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class ApplyCustomerServiceReqDTO implements Serializable {
    private static final long serialVersionUID = 5112281004221653772L;

    /**
     * 群id
     */
    @NotBlank(message = "群id不能为空")
    private Long groupId;

    private String orderNo;

    /**
     * 申请场景
     */
    private String applyScene;

    /**
     * 申请原因
     */
    private String reason;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 语言
     */
    private String language;

    /**
     * 申请人操作员编号
     */
    @NotBlank(message = "申请人操作员编号不能为空")
    private String operatorNo;

    /**
     * 申请人操作员类型
     */
    @NotNull(message = "申请人操作员类型不能为空")
    private Integer operatorType;
}
