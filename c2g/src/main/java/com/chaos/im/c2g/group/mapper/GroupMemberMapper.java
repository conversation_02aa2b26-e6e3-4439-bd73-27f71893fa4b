package com.chaos.im.c2g.group.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaos.im.c2g.group.domain.GroupMemberDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
@Mapper
public interface GroupMemberMapper extends BaseMapper<GroupMemberDO> {
    List<String> list(IPage<String> page, @Param("memberId") String memberId);

    void deleteByGroupId(@Param("groupId") Long groupId);
}
