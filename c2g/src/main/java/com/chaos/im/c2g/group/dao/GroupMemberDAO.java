package com.chaos.im.c2g.group.dao;


import com.chaos.im.c2g.group.api.domain.GroupMemberQuery;
import com.chaos.im.c2g.group.domain.GroupMemberDO;
import com.chaos.keep.alive.common.core.domain.BasePage;
import com.chaos.keep.alive.common.persistent.dao.BaseDAO;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface GroupMemberDAO extends BaseDAO<GroupMemberDO> {

    BasePage<GroupMemberDO> listByPage(GroupMemberQuery query);

    long count(Long groupId, String memberId);

    List<String> getByGroupId(Long groupId);

    BasePage<String> listGroupMemberByPage(GroupMemberQuery query, String memberId);

    long count(Long groupId);

    void deleteByGroupId(Long groupId);
}
