<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chaos.im.c2g.group.mapper.GroupMemberMapper">
    <delete id="deleteByGroupId">
        DELETE
        FROM group_member
        WHERE group_id = #{groupId}
    </delete>

    <select id="list" resultType="java.lang.String">
        <![CDATA[
        SELECT DISTINCT(member_id)
        FROM group_member
        where group_id in (
            SELECT group_id
            FROM group_member
            where member_id = #{memberId})
          and member_id <> #{memberId}
        ]]>
    </select>
</mapper>