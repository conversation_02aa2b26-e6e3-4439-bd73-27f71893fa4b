package com.chaos.im.c2g.group.domain;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.outstanding.framework.core.BaseEnum;

import lombok.Getter;

@Getter
public enum GroupApplyStatusEnum implements BaseEnum<GroupApplyStatusEnum, Integer> {
    PENDING(10, "待处理"),
    PROCESSING(11, "处理中"),
    HANDLED(12, "处理完成");

    GroupApplyStatusEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    @EnumValue
    private Integer code;
    private String message;

    public Integer getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }

    public static GroupApplyStatusEnum getByCode(Integer code) {
        if (code != null) {
            for (GroupApplyStatusEnum status : GroupApplyStatusEnum.values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
        }
        return null;
    }
}
