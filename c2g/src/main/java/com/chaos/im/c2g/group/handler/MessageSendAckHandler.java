package com.chaos.im.c2g.group.handler;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.chaos.im.c2g.group.domain.C2gMessageBO;
import com.chaos.im.chat.api.ChatApi;
import com.chaos.keep.alive.common.im.domain.MessageSendJsonRequest;
import com.chaos.keep.alive.common.im.domain.MessageSendJsonResponse;
import com.chaos.keep.alive.common.web.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.kafka.core.KafkaTemplate;

import java.util.List;

@Slf4j
public class MessageSendAckHandler extends MessageSendHandler {

    private ChatApi chatApi;
    private String TOPIC_MESSAGE_SEND_RESPONSE;
    private MessageSavingContext messageSavingContext;
    private KafkaTemplate<String, String> kafkaTemplate;
    private volatile boolean initialized = false;

    public MessageSendAckHandler() {
        // 延迟初始化，在第一次使用时初始化依赖
    }

    private void initDependencies() {
        if (!initialized && SpringContextUtils.getApplicationContext() != null) {
            synchronized (this) {
                if (!initialized) {
                    try {
                        this.chatApi = SpringContextUtils.getBean("chatApi", ChatApi.class);
                        this.messageSavingContext = SpringContextUtils.getBean(MessageSavingContext.class);
                        this.kafkaTemplate = SpringContextUtils.getBean("kafkaTemplate", KafkaTemplate.class);

                        // 获取配置值
                        String[] activeProfiles = SpringContextUtils.getApplicationContext().getEnvironment().getActiveProfiles();
                        String activeProfile = activeProfiles.length > 0 ? activeProfiles[0] : "local";
                        this.TOPIC_MESSAGE_SEND_RESPONSE = "topic-message-send-response-" + activeProfile;

                        this.initialized = true;
                        log.info("MessageSendAckHandler dependencies initialized successfully");
                    } catch (Exception e) {
                        this.TOPIC_MESSAGE_SEND_RESPONSE = "topic-message-send-response-local";
                        log.error("Failed to initialize dependencies", e);
                        throw new RuntimeException("Failed to initialize MessageSendAckHandler dependencies", e);
                    }
                }
            }
        }
    }

    @Override
    public boolean isSupport(Long chatId, List<MessageSendJsonRequest> request) {
        return true;
    }

    @Override
    public void handleRequest(Long chatId, List<MessageSendJsonRequest> requests) {
        initDependencies();
        String groupId = chatApi.getPeerIdByChatId(chatId);
        for (MessageSendJsonRequest request : requests) {

            try {
                C2gMessageBO c2gMessageBO = getC2gMessageBO(request, groupId);
                IMessageWriteHandler messageWriteHandler = messageSavingContext.init();
                messageWriteHandler.handle(chatId, c2gMessageBO);
                sendMessageSendResponse(request);
            } catch (Exception e) {
                log.error("MessageSendAckHandler handleRequest error", e);
            }
        }
    }

    public void sendMessageSendResponse(MessageSendJsonRequest request) {
        log.info("MessageSendAckHandler sendMessageSendResponse start request:{}", JSONUtil.toJsonStr(request));
        initDependencies();
        MessageSendJsonResponse message = new MessageSendJsonResponse();
        message.setChatId(request.getChatId());
        message.setMessageId(request.getMessageId());
        message.setFromOperatorNo(request.getFromOperatorNo());
        message.setToOperatorNo(StringUtils.isEmpty(request.getToOperatorNo()) ? request.getFromOperatorNo() : request.getToOperatorNo());
        message.setChatType(request.getChatType());
        message.setSequence(request.getSequence());
        kafkaTemplate.send(TOPIC_MESSAGE_SEND_RESPONSE, JSONObject.toJSONString(message));
        log.info("MessageSendAckHandler sendMessageSendResponse end request:{}", JSONUtil.toJsonStr(request));
    }

    private static C2gMessageBO getC2gMessageBO(MessageSendJsonRequest request, String groupId) {
        C2gMessageBO c2gMessageBO = new C2gMessageBO();
        c2gMessageBO.setId(request.getMessageId());
        c2gMessageBO.setCategory(request.getCategory());
        c2gMessageBO.setGroupId(groupId == null ? 0 : Long.parseLong(groupId));
        c2gMessageBO.setAppId(request.getToAppId());
        c2gMessageBO.setFromOperatorNo(request.getFromOperatorNo());
        c2gMessageBO.setContent(request.getContent());
        c2gMessageBO.setChatId(request.getChatId());
        c2gMessageBO.setMessageId(request.getChatId() + "-" + request.getMessageId());
        c2gMessageBO.setSequence(request.getSequence());
        c2gMessageBO.setTimestamp(System.currentTimeMillis());
        return c2gMessageBO;
    }

}
