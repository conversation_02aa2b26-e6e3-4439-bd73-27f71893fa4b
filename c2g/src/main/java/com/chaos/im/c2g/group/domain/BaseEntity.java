package com.chaos.im.c2g.group.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.outstanding.framework.core.DelStateEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class BaseEntity implements Serializable {

    @TableId(
            type = IdType.INPUT
    )
    protected Long id;
    @TableField(
            fill = FieldFill.INSERT
    )
    protected Date createTime;
    @TableField(
            fill = FieldFill.INSERT_UPDATE
    )
    protected Date updateTime;
    @TableField(
            fill = FieldFill.INSERT
    )
    protected DelStateEnum delState;
    @TableField(
            fill = FieldFill.INSERT
    )
    protected Long version;

}
