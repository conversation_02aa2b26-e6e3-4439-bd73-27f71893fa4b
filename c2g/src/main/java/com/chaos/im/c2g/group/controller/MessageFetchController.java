package com.chaos.im.c2g.group.controller;

import com.chaos.im.c2g.group.api.domain.FetchQuery;
import com.chaos.im.c2g.group.service.MessageSendService;
import com.outstanding.framework.core.ResponseDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fetch")
public class MessageFetchController {

    @Autowired
    private MessageSendService messageSendService;

    @PostMapping("/offline/msg")
    public ResponseDTO<?> fetch(@RequestBody FetchQuery fetchQuery) {
        return ResponseDTO.creatDTO(messageSendService.fetch(
                fetchQuery.getChatId(),
                fetchQuery.getOperatorNo(),
                fetchQuery.getDeviceId(),
                fetchQuery.getStartMessageId(),
                fetchQuery.getStopMessageId(),
                fetchQuery.getPageSize(),fetchQuery.getCurrent(),fetchQuery.getSort()));
    }

    @GetMapping("/lastMessageAck")
    public ResponseDTO<?> getLastMessageAck(FetchQuery fetchQuery) {
        return ResponseDTO.creatDTO(messageSendService.lastMessageAck(
                fetchQuery.getChatId(),
                fetchQuery.getOperatorNo(),
                fetchQuery.getDeviceId()));
    }
}
