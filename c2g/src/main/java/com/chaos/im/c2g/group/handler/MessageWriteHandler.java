package com.chaos.im.c2g.group.handler;

import com.chaos.im.c2g.group.domain.C2gMessageBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.extension.Adaptive;

@Slf4j
public abstract class MessageWriteHandler implements IMessageWriteHandler {
    protected IMessageWriteHandler nextHandler;

    @Override
    public void setNextHandler(IMessageWriteHandler next) {
        this.nextHandler = next;
    }

    @Adaptive
    public void handle(Long chatId, C2gMessageBO c2gMessageBO) {
        if(isSupport(chatId,c2gMessageBO)) {
            handleRequest(chatId, c2gMessageBO);
        }
        if (nextHandler != null) {
            log.debug("next handler is {}", nextHandler.getClass().getName());
            nextHandler.handle(chatId, c2gMessageBO);
        }
    }

    public abstract boolean isSupport(Long chatId,C2gMessageBO c2gMessageBO);

    public abstract void handleRequest(Long chatId, C2gMessageBO c2gMessageBO);
}
