package com.chaos.im.c2g.group.handler;

import com.chaos.keep.alive.common.im.domain.MessageSendJsonRequest;
import org.apache.dubbo.common.extension.SPI;

import java.util.List;
@SPI
public interface IMessageSendHandler {
    void setNextHandler(IMessageSendHandler next);

    void handle(Long chatId, List<MessageSendJsonRequest> request);

    boolean isSupport(Long chatId, List<MessageSendJsonRequest> request);

    void handleRequest(Long chatId, List<MessageSendJsonRequest> request);
}
