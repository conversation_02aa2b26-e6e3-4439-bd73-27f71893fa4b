package com.chaos.im.c2g.group.dao;


import com.chaos.im.c2g.group.domain.C2gMessageAckDO;
import com.chaos.keep.alive.common.persistent.dao.BaseDAO;

import java.util.List;

public interface C2gMessageAckDAO extends BaseDAO<C2gMessageAckDO> {

    List<C2gMessageAckDO> listByMemberId(Long memberId);

    void updateAck(Long chatId, String memberId, String deviceId, Long messageId,Long sequence);

    long count(Long chatId, String memberId, String deviceId);

    Long getLastAckMessageId(Long chatId, String memberId, String deviceId);
}
