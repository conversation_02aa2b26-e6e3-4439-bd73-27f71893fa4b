package com.chaos.im.c2g.group.handler;

import cn.hutool.json.JSONUtil;
import com.chaos.im.c2g.group.domain.C2gMessageBO;
import com.chaos.im.c2g.group.service.GroupMemberService;
import com.chaos.keep.alive.common.im.constant.ImConstants;
import com.chaos.keep.alive.common.web.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.List;
import java.util.Objects;

import static com.chaos.im.c2g.group.service.impl.GroupMemberServiceImpl.GROUP_KEY_PREFIX;

@Slf4j
public class UnReadCountWriteHandler extends MessageWriteHandler {
    private RedissonClient redissonClient;

    private RedisTemplate<String, String> redisTemplate;

    private GroupMemberService groupMemberService;

    private volatile boolean initialized = false;

    private void initDependency() {
        if (!initialized && SpringContextUtils.getApplicationContext() != null) {
            synchronized (this) {
                if (!initialized) {
                    try {
                        this.redissonClient = SpringContextUtils.getBean(RedissonClient.class);
                        this.redisTemplate = SpringContextUtils.getBean("redisTemplate",RedisTemplate.class);
                        this.groupMemberService = SpringContextUtils.getBean(GroupMemberService.class);

                        this.initialized = true;
                    }catch (Exception e){
                        log.error("Failed to initialize dependencies", e);
                        throw new RuntimeException("Failed to initialize UnReadCountWriteHandler dependencies", e);
                    }
                }
            }
        }
    }

    @Override
    public boolean isSupport(Long chatId, C2gMessageBO c2gMessageBO) {
        initDependency();
        List<String> memberIds = getMemberIds(chatId, c2gMessageBO.getGroupId().toString());
        return CollectionUtils.isNotEmpty(memberIds);
    }

    private List<String> getMemberIds(Long chatId, String groupId) {
        initDependency();
        List<String> memberIds = redisTemplate.opsForList().range(GROUP_KEY_PREFIX + "::" + chatId, 0, -1);
        log.info("get memberIds from redis by chatId :{}", memberIds);
        log.info("根据chatId:{} 获取groupId:{}", chatId, groupId);
        if (CollectionUtils.isEmpty(memberIds) && StringUtils.isNotEmpty(groupId)) {
            memberIds = groupMemberService.getByGroupId(Long.parseLong(groupId));
            log.info("get memberIds from db by groupId : {}", memberIds);
        }
        return memberIds;
    }

    @Override
    public void handleRequest(Long chatId, C2gMessageBO c2gMessageBO) {
        log.debug("<chatId>:{} <c2gMessageBO>:{}", chatId, JSONUtil.toJsonStr(c2gMessageBO));

        // 使用统一的方法获取成员列表
        List<String> memberIds = getMemberIds(chatId, c2gMessageBO.getGroupId().toString());

        if (CollectionUtils.isNotEmpty(memberIds)) {
            for (String memberId : memberIds) {
                if (!Objects.equals(memberId, c2gMessageBO.getFromOperatorNo())) {
                    String unReadCountKey = ImConstants.C2G_UNREAD_COUNT_PREFIX + chatId + "::" + memberId;
                    redissonClient.getAtomicLong(unReadCountKey).incrementAndGet();
                }
            }
        }
    }
}
