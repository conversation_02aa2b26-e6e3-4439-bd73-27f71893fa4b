package com.chaos.im.c2g.group.domain;

import com.outstanding.framework.core.BaseEnum;

public enum GroupInfoOpTypeEnum implements BaseEnum<GroupInfoOpTypeEnum, Integer> {
    INVITE_CUSTOMER_SERVICE(10, "邀请客服"),
    JOIN_CUSTOMER_SERVICE(11, "客服加入"),
    FINISH_CUSTOMER_SERVICE(12, "结束服务");

    GroupInfoOpTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    private Integer code;
    private String message;

    public Integer getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }
}
