package com.chaos.im.c2g.group.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaos.im.c2g.group.api.domain.GroupDTO;
import com.chaos.im.c2g.group.api.domain.GroupMemberQuery;
import com.chaos.im.c2g.group.dao.GroupDAO;
import com.chaos.im.c2g.group.dao.GroupMemberDAO;
import com.chaos.im.c2g.group.domain.*;
import com.chaos.im.c2g.group.mapper.GroupMemberMapper;
import com.chaos.im.c2g.group.service.GroupMemberService;
import com.chaos.im.chat.api.ChatApi;
import com.chaos.im.chat.domain.ChatDTO;
import com.chaos.keep.alive.common.core.domain.BasePage;
import com.chaos.keep.alive.common.core.domain.Pagination;
import com.chaos.keep.alive.common.core.exception.BusinessException;
import com.chaos.keep.alive.common.core.util.BeanCopierUtils;
import com.chaos.keep.alive.common.im.constant.Constants;
import com.chaos.usercenter.api.UserOperatorFacade;
import com.chaos.usercenter.api.dto.resp.OperatorBatchQueryDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class GroupMemberServiceImpl extends ServiceImpl<GroupMemberMapper, GroupMemberDO> implements GroupMemberService {

    public static final String GROUP_KEY_PREFIX = "GROUP";

    @Autowired
    private GroupMemberDAO groupMemberDAO;

    @Autowired
    private GroupMemberMapper groupMemberMapper;

    @Autowired
    private GroupDAO groupDAO;

    @Resource
    private UserOperatorFacade userOperatorFacade;

    @Resource
    private ChatApi chatApi;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private RedissonClient redissonClient;

    @Override
    public BasePage<GroupMemberDTO> listByPage(GroupMemberQuery query) {
        return BeanCopierUtils.convert(groupMemberDAO.listByPage(query), GroupMemberDTO.class);
    }

    @Override
    @Transactional(isolation = Isolation.READ_UNCOMMITTED)
    public void join(GroupDTO groupDTO) {
        long count = groupMemberDAO.count(groupDTO.getId(), groupDTO.getOwnerId());
        if (count > 0) {
            throw new BusinessException("已加入群组");
        }
//        String key = "join_group_lock_" + groupDTO.getId();
//        RLock lock = redissonClient.getLock(key);
//        try {
//            加锁
//            lock.lock();
            saveMember(groupDTO, groupDTO.getOwnerId());
//        } finally {
//            解锁
//            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
//                lock.unlock();
//            }
//        }
        ChatDTO chatDTO = new ChatDTO();
        chatDTO.setType(Constants.CHAT_TYPE_C2G);
        chatDTO.setMemberId(groupDTO.getOwnerId());
        chatDTO.setPeerId(groupDTO.getId() + "");
        chatDTO.setNickname(groupDTO.getName());
        chatDTO.setAvatar(groupDTO.getFaceUrl());
        chatDTO.setEx(groupDTO.getEx());
        // 添加一个会话
        chatApi.addChat(chatDTO);
    }

    @Transactional(isolation = Isolation.READ_UNCOMMITTED)
    @Override
    public void join(Long groupId, String memberId) {

        Optional<GroupDO> optional = groupDAO.getById(groupId);
        if (!optional.isPresent()) {
            throw new BusinessException("群组不存在");
        }

        long count = groupMemberDAO.count(groupId, memberId);
        if (count > 0) {
            return;
//            throw new BusinessException("已加入群组");
        }
        String key = "join_group_lock_" + groupId;
        RLock lock = redissonClient.getLock(key);
        try {
            //加锁
            lock.lock();
            GroupDTO groupDTO = new GroupDTO();
            BeanCopierUtils.copy(optional.get(), groupDTO);

            saveMember(groupDTO, memberId);

        } finally {
            //解锁
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        ChatDTO chatDTO = new ChatDTO();
        chatDTO.setType(Constants.CHAT_TYPE_C2G);
        chatDTO.setMemberId(memberId);
        chatDTO.setPeerId(optional.get().getId() + "");
        chatDTO.setNickname(optional.get().getName());
        chatDTO.setAvatar(optional.get().getFaceUrl());
        // 添加一个会话
        log.info("添加一个会话 chatDTO:{}", JSONUtil.toJsonStr(chatDTO));
        chatApi.addChat(chatDTO);

    }

    @Override
    public void kick(Long groupId, String memberId) {
//        long count = groupMemberDAO.count(groupId, memberId);
//        if (count <= 0) {
//            throw new BusinessException("未加入群组");
//        }
        String key = "join_group_lock_" + groupId;
        RLock lock = redissonClient.getLock(key);
        Optional<GroupDO> optional = groupDAO.getById(groupId);
        if (optional.isPresent()) {
            try {
                //加锁
                lock.lock();
                deleteMemberByMemberId(groupId, memberId);
            } finally {
                //解锁
                if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }

        chatApi.deleteChatMember(Constants.CHAT_TYPE_C2G, groupId, memberId);
    }


    public void deleteMemberByMemberId(Long groupId, String memberId) {
        redisTemplate.delete(GROUP_KEY_PREFIX + "::" + groupId);
        QueryWrapper queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("member_id", memberId);
        queryWrapper.eq("group_id", groupId);
        remove(queryWrapper);
    }

    public void saveMember(GroupDTO groupDTO, String memberId) {
        long count = groupMemberDAO.count(groupDTO.getId());
        if (count >= 100) {
            throw new BusinessException("群成员已满");
        }
        log.info("群成员数量：{}", count);
        redisTemplate.delete(GROUP_KEY_PREFIX + "::" + groupDTO.getId());
        GroupMemberDO groupMemberDO = new GroupMemberDO();
        groupMemberDO.setGroupId(groupDTO.getId());
        groupMemberDO.setMemberId(memberId);
        groupMemberDAO.save(groupMemberDO);
    }

    @Override
    public List<String> getByGroupId(Long groupId) {
        List<String> cachedMemberIds = redisTemplate.opsForList().range(GROUP_KEY_PREFIX + "::" + groupId, 0, -1);
        if (CollectionUtils.isEmpty(cachedMemberIds)) {
            String key = "get_member" + groupId;
            RLock lock = redissonClient.getLock(key);
            try {
                //加锁
                lock.lock();
                log.info("锁住");
                cachedMemberIds = redisTemplate.opsForList().range(GROUP_KEY_PREFIX + "::" + groupId, 0, -1);
                if (CollectionUtils.isEmpty(cachedMemberIds)) {
                    cachedMemberIds = groupMemberDAO.getByGroupId(groupId);
                } else return cachedMemberIds;
                if (CollectionUtils.isNotEmpty(cachedMemberIds)) {
                    log.info("添加缓存");
                    // 添加到缓存
                    redisTemplate.opsForList().rightPushAll(GROUP_KEY_PREFIX + "::" + groupId, cachedMemberIds);
                }
            } finally {
                //解锁
                if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
        return cachedMemberIds;
    }

    @Override
    public BasePage<GroupMemberDTO> listGroupMember(GroupMemberQuery query) {

        IPage<GroupMemberDO> pageVo = new Page<>(query.getCurrent(), query.getPageSize());

        pageVo =  selectPageByGroupId(query, pageVo);

        Pagination pagination = new Pagination(pageVo.getTotal(), pageVo.getSize(), pageVo.getCurrent());

        Map<String, OperatorBatchQueryDTO> userMap = userOperatorFacade.queryByOperatorNoList(pageVo.getRecords().stream()
                        .map(GroupMemberDO::getMemberId).collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(OperatorBatchQueryDTO::getOperatorNo, it -> it));
        return new BasePage<>(pageVo.getRecords().stream().map(member -> {
            GroupMemberDTO groupMemberDTO = new GroupMemberDTO();
            groupMemberDTO.setId(member.getId());
            groupMemberDTO.setGroupId(member.getGroupId());
            groupMemberDTO.setMemberId(member.getMemberId());
            OperatorBatchQueryDTO userAccountDTO = userMap.get(member.getMemberId());
            groupMemberDTO.setNickname(userAccountDTO.getNickname());
            groupMemberDTO.setAvatar(userAccountDTO.getAvatarUrl());
            return groupMemberDTO;
        }).collect(Collectors.toList()), pagination);

    }

    private IPage<GroupMemberDO> selectPageByGroupId(GroupMemberQuery query, IPage pageVo) {
        QueryWrapper<GroupMemberDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("group_id", query.getGroupId());

       return groupMemberMapper.selectPage(pageVo,queryWrapper);
    }

    @Override
    public void dismiss(Long groupId) {
        groupMemberDAO.deleteByGroupId(groupId);
    }
}
