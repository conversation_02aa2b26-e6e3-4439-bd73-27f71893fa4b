package com.chaos.im.c2g.group.handler;

import cn.hutool.json.JSONUtil;
import com.chaos.im.c2g.group.domain.C2gMessageBO;
import com.chaos.keep.alive.common.web.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

@Slf4j
public class C2gMessageWriteHandler extends MessageWriteHandler {
    private  MongoTemplate mongoTemplate;

    private volatile boolean initialized = false;

    private void initDependency(){
        if (!initialized && SpringContextUtils.getApplicationContext() != null) {
            synchronized (this) {
                if (!initialized) {
                    try {
                        this.mongoTemplate = SpringContextUtils.getBean(MongoTemplate.class);
                        this.initialized = true;
                        log.info("C2gMessageWriteHandler dependencies initialized successfully");
                    } catch (Exception e) {
                        log.error("Failed to initialize dependencies", e);
                    }
                }
            }
        }
    }

    @Override
    public boolean isSupport(Long chatId, C2gMessageBO c2gMessageBO) {
        return c2gMessageBO != null;
    }

    @Override
    public void handleRequest(Long chatId, C2gMessageBO c2gMessageBO) {
        initDependency();
        log.info("落库mongodb ,c2gMessageBO:{}", JSONUtil.toJsonStr(c2gMessageBO));
        mongoTemplate.save(c2gMessageBO);
    }
}
