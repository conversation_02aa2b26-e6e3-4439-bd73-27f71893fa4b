package com.chaos.im.c2g.group.service;


import com.chaos.im.c2g.group.api.domain.LastC2gMessageDTO;
import com.chaos.im.c2g.group.api.domain.MessageSendReq;
import com.chaos.keep.alive.common.im.domain.MessageJsonPush;
import com.chaos.keep.alive.common.im.domain.MessageSendJsonRequest;
import com.outstanding.framework.core.PageInfoDTO;

import java.util.List;
import java.util.Map;

public interface MessageSendService {

    void save(List<MessageSendJsonRequest> requests, Long chatId);

    void updateAck(Long chatId, String memberId, String clientId, Long messageId,Long sequence);

    PageInfoDTO<MessageJsonPush> fetch(Long chatId, String operatorNo, String deviceId, Long startMessageId, Long stopMessageId, int size, int current, Map<String,String> sort);

    Long lastMessageAck(Long chatId, String operatorNo, String deviceId);

    void sendMessage(MessageSendReq req);

    LastC2gMessageDTO getLastMessageByChatIdAndOperatorNo(Long chatId, String operatorNo, String deviceId);



}
