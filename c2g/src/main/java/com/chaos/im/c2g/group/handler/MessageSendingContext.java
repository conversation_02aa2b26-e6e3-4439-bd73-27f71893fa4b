package com.chaos.im.c2g.group.handler;

import org.apache.dubbo.common.extension.ExtensionLoader;
import org.springframework.stereotype.Component;

@Component
public class MessageSendingContext {

    public IMessageSendHandler init() {
        // 获取具体的实现类实例，避免适配器问题
        MessageSendAckHandler messageAckHandler = (MessageSendAckHandler) ExtensionLoader.getExtensionLoader(IMessageSendHandler.class).getExtension("ack");
        MessageSendPushHandler messagePushHandler = (MessageSendPushHandler) ExtensionLoader.getExtensionLoader(IMessageSendHandler.class).getExtension("push");

        // 直接在具体实现类上调用 setNext，避免 Dubbo 适配器问题
        messageAckHandler.setNextHandler(messagePushHandler);

        return messageAckHandler;
    }
}
