package com.chaos.im.c2g.group.handler;

import com.chaos.keep.alive.common.im.domain.MessageSendJsonRequest;
import lombok.Setter;
import org.apache.dubbo.common.extension.Adaptive;

import java.util.List;

public abstract class MessageSendHandler implements IMessageSendHandler {

    protected IMessageSendHandler nextHandler;

    @Override
    public void setNextHandler(IMessageSendHandler next) {
        this.nextHandler = next;
    }

    @Override
    @Adaptive
    public void handle(Long chatId, List<MessageSendJsonRequest> request) {
        if(isSupport(chatId,request)) {
            handleRequest(chatId, request);
        }
        if (nextHandler != null) {
            nextHandler.handle(chatId, request);
        }
    }
}
