package com.chaos.im.c2g.group.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.chaos.im.c2g.group.api.domain.GroupDTO;
import com.chaos.im.c2g.group.domain.GroupDO;
import com.chaos.im.c2g.group.domain.GroupQuery;
import com.chaos.keep.alive.common.core.domain.BasePage;
import com.outstanding.framework.core.ResponseDTO;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface GroupService  extends IService<GroupDO> {

    BasePage<GroupDTO> listByPage(GroupQuery query);

    Long create(GroupDTO groupDTO);

    void dismiss(Long groupId);

    GroupDO findByGroupId(Long groupId);
    GroupDO findByChatId(Long chatId);

    GroupDO queryByOrderNo(String ownerId, String orderNo, Integer bizType);

    @Transactional
    ResponseDTO<Long> createAndInvite(GroupDTO groupDTO,List<String> memersOperatorNo);
}
