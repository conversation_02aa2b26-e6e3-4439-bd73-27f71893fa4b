package com.chaos.im.c2g.group.service;

import com.chaos.im.c2g.group.api.domain.GroupApplyCountReqDTO;
import com.chaos.im.c2g.group.api.domain.GroupApplyCountRespDTO;
import com.chaos.im.c2g.group.api.domain.GroupApplyDetailReqDTO;
import com.chaos.im.c2g.group.api.domain.GroupApplyRespDTO;
import com.chaos.im.c2g.group.api.domain.GroupApplySearchReqDTO;
import com.chaos.im.c2g.group.api.domain.ReadApplyReqDTO;
import com.chaos.im.c2g.group.domain.AcceptApplyReqDTO;
import com.chaos.im.c2g.group.domain.ApplyCustomerServiceReqDTO;
import com.chaos.im.c2g.group.domain.HandleApplyReqDTO;
import com.outstanding.framework.core.PageInfoDTO;
import com.outstanding.framework.core.PendingException;

public interface GroupApplyService {

    void applyCustomerService(ApplyCustomerServiceReqDTO reqDTO) throws PendingException;

    void acceptGroupApply(AcceptApplyReqDTO reqDTO) throws PendingException;

    void handleGroupApply(HandleApplyReqDTO reqDTO) throws PendingException;



     /**
     * 分页查询客服介入申请列表
     *
     * @param reqDTO
     * @return
     * @throws PendingException
     */
    PageInfoDTO<GroupApplyRespDTO> pageGroupApply(GroupApplySearchReqDTO reqDTO) throws PendingException;


       /**
     * 申请单状态数量统计
     *
     * @param reqDTO
     * @return
     * @throws PendingException
     */
    GroupApplyCountRespDTO countGroupApply(GroupApplyCountReqDTO reqDTO) throws PendingException;

    /**
     * 查询客服介入申请详情
     *
     * @param reqDTO
     * @return
     * @throws PendingException
     */
    GroupApplyRespDTO getGroupApplyDetail(GroupApplyDetailReqDTO reqDTO) throws PendingException;


        /**
     * 已读申请单
     *
     * @param reqDTO
     * @throws PendingException
     */
    void readGroupApply(ReadApplyReqDTO reqDTO) throws PendingException;
}
