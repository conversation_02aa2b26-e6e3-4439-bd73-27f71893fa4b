package com.chaos.im.c2g.group.service;


import com.chaos.im.c2g.group.api.domain.GroupDTO;
import com.chaos.im.c2g.group.api.domain.GroupMemberQuery;
import com.chaos.im.c2g.group.domain.GroupMemberDTO;
import com.chaos.keep.alive.common.core.domain.BasePage;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface GroupMemberService {

    BasePage<GroupMemberDTO> listByPage(GroupMemberQuery query);

    void join(GroupDTO groupDTO);

    @Transactional(isolation = Isolation.READ_UNCOMMITTED)
    void join(Long groupId, String memberId);


    @Transactional(isolation = Isolation.READ_UNCOMMITTED)
    void kick(Long groupId, String memberId);

    List<String> getByGroupId(Long groupId);

    BasePage<GroupMemberDTO> listGroupMember(GroupMemberQuery query);

    void dismiss(Long groupId);

}
