package com.chaos.im.c2g.group.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaos.im.api.ImCustomerServiceFacade;
import com.chaos.im.api.dto.req.ImCustomerServiceGetReqDTO;
import com.chaos.im.api.dto.req.ImGroupApplyCountReqDTO;
import com.chaos.im.api.dto.req.ImGroupApplyDetailReqDTO;
import com.chaos.im.api.dto.req.ImReadApplyReqDTO;
import com.chaos.im.api.dto.resp.ImCustomerServiceRespDTO;
import com.chaos.im.api.dto.resp.ImGroupApplyCountRespDTO;
import com.chaos.im.api.dto.resp.ImGroupApplyRespDTO;
import com.chaos.im.c2g.group.api.domain.*;
import com.chaos.im.c2g.group.domain.*;
import com.chaos.im.c2g.group.mapper.GroupApplyMapper;
import com.chaos.im.c2g.group.service.GroupApplyService;
import com.chaos.im.c2g.group.service.GroupMemberService;
import com.chaos.im.c2g.group.service.GroupService;
import com.chaos.keep.alive.common.im.domain.ImCodeEnum;
import com.chaos.keep.alive.common.web.util.PageUtil;
import com.chaos.usercenter.api.UserOperatorFacade;
import com.chaos.usercenter.api.dto.resp.UserOperatorInfoForImDTO;
import com.outstanding.framework.core.DelStateEnum;
import com.outstanding.framework.core.PageInfoDTO;
import com.outstanding.framework.core.PendingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GroupApplyServiceImpl extends ServiceImpl<GroupApplyMapper, GroupApplyBO> implements GroupApplyService {

    @Autowired
    private GroupService groupService;

    @Autowired
    private GroupMemberService groupMemberService;

    @DubboReference(version = "1.0.0")
    private UserOperatorFacade userOperatorFacade;

    @DubboReference(version = "1.0.0")
    private ImCustomerServiceFacade imCustomerServiceFacade;

    private static final String EX_ORDER_NO = "orderNo";
    private static final String EX_CUSTOMER_SERVICE_STATUS = "customerServiceStatus";
    private static final String EX_DISMISS_REASON = "dismissReason";
    private static final String EX_OP_TYPE = "opType";
    private static final String EX_OP_USER = "opUser";
    private static final String EX_OP_USER_ID = "userId";
    private static final String EX_OP_USER_NICKNAME = "nickname";

    @Override
    public void applyCustomerService(ApplyCustomerServiceReqDTO reqDTO) throws PendingException {
        long count = this.count(Wrappers.<GroupApplyBO>lambdaQuery()
                .eq(GroupApplyBO::getGroupId, reqDTO.getGroupId())
                .eq(GroupApplyBO::getStatus, GroupApplyStatusEnum.PENDING)
                .eq(GroupApplyBO::getDelState, DelStateEnum.NORMAL));
        if (count > 0) {
            throw new PendingException(ImCodeEnum.I1026);
        }

        GroupDO group = groupService.getOne(Wrappers.<GroupDO>lambdaQuery().eq(GroupDO::getId, reqDTO.getGroupId()));
        if (group == null) {
            throw new PendingException(ImCodeEnum.I1022);
        }
        if (Objects.equals(group.getStatus(), GroupStatusEnum.DISMISS.getCode())) {
            throw new PendingException(ImCodeEnum.I1037);
        }

        UserOperatorInfoForImDTO userOperatorInfoForImDTO = userOperatorFacade
                .getOperatorInfoForIM(reqDTO.getOperatorNo());
        // 修改群信息
        this.updateGroupInfo(reqDTO.getGroupId(), GroupCustomerServiceStatusEnum.WAIT_JOIN,
                GroupInfoOpTypeEnum.INVITE_CUSTOMER_SERVICE,
                userOperatorInfoForImDTO.getOperatorNo(), userOperatorInfoForImDTO.getNickName());

        // 申请单
        GroupApplyBO apply = new GroupApplyBO();
        apply.setApplyTime(new Date());
        apply.setApplyScene(reqDTO.getApplyScene());
        apply.setStatus(GroupApplyStatusEnum.PENDING.getCode());
        apply.setReason(reqDTO.getReason());
        apply.setGroupId(reqDTO.getGroupId());
        apply.setBusinessLine(reqDTO.getBusinessLine());
        apply.setOperatorNo(reqDTO.getOperatorNo());
        apply.setOperatorType(reqDTO.getOperatorType());
        apply.setNickname(userOperatorInfoForImDTO.getNickName());
        // apply.setStoreName(userOperatorInfoForImDTO.getStoreName() == null ? null :
        // operator.getStoreName().toJSONString());
        apply.setOrderNo(reqDTO.getOrderNo());
        apply.setLanguage(reqDTO.getLanguage());
        apply.setGroupOperatorNo(userOperatorInfoForImDTO.getOperatorNo());
        apply.setGroupMobile(userOperatorInfoForImDTO.getMobile());
        apply.setReadStatus(GroupApplyReadStatusEnum.UNREAD.getCode());
        this.save(apply);

        // //BOSS通知推送
        // asyncTaskExecutor.execute(() -> {
        // webSocketPush.wsPush(WebSocketPush.ORDER_CONSULT, consultRoleNos, null,
        // null);
        // });
    }

    @Override
    public void acceptGroupApply(AcceptApplyReqDTO reqDTO) throws PendingException {
        GroupApplyBO apply = this
                .getOne(Wrappers.<GroupApplyBO>lambdaQuery().eq(GroupApplyBO::getId, reqDTO.getApplyId()));
        if (apply == null) {
            throw new PendingException(ImCodeEnum.I1018);
        }

        if (apply.getStatus() != GroupApplyStatusEnum.PENDING.getCode()) {
            throw new PendingException(ImCodeEnum.I1027);
        }

        ImCustomerServiceGetReqDTO imCustomerServiceGetReqDTO = new ImCustomerServiceGetReqDTO();
        imCustomerServiceGetReqDTO.setOperatorNo(reqDTO.getOperatorNo());
        ImCustomerServiceRespDTO imCustomerServiceRespDTO = imCustomerServiceFacade
                .getCustomerService(imCustomerServiceGetReqDTO);
        // 当前操作员注册im
        if (imCustomerServiceRespDTO == null) {
            throw new PendingException(ImCodeEnum.I1043);
        }

        // 客服进群
        GroupDO groupDO = groupService.getById(apply.getGroupId());
        if (groupDO.getStatus() != null && groupDO.getStatus().equals(GroupStatusEnum.DISMISS.getCode())) {
            log.info("该群聊已被解散, 客服无法进群, groupId: {}, applyId: {}", apply.getGroupId(), apply.getId());
        } else {
            log.info("开始邀请用户进群accept, applyId:{}", reqDTO.getApplyId());
            groupMemberService.join(apply.getGroupId(), imCustomerServiceRespDTO.getOperatorNo());
            log.info("结束邀请用户进群accept, applyId:{}", reqDTO.getApplyId());
        }

        // 修改群信息
        this.updateGroupInfo(apply.getGroupId(), GroupCustomerServiceStatusEnum.HAS_JOIN,
                GroupInfoOpTypeEnum.JOIN_CUSTOMER_SERVICE, imCustomerServiceRespDTO.getOperatorNo(),
                imCustomerServiceRespDTO.getRealName());

        // 更新状态
        apply.setAcceptOperatorNo(imCustomerServiceRespDTO.getOperatorNo());
        apply.setAcceptOperatorName(imCustomerServiceRespDTO.getRealName());
        apply.setAcceptTime(new Date());
        apply.setStatus(GroupApplyStatusEnum.PROCESSING.getCode());
        this.updateById(apply);
    }

    private void updateGroupInfo(Long groupId, GroupCustomerServiceStatusEnum customerServiceStatus,
            GroupInfoOpTypeEnum opType, String opUserId, String opNickname) {
        // 修改群信息
        GroupDO group = groupService.getById(groupId);
        if (group.getStatus() != null && group.getStatus().equals(GroupStatusEnum.DISMISS.getCode())) {
            log.info("该群聊已被解散,无法修改群信息, groupId: {}", groupId);
            return;
        }

        JSONObject extObj = StringUtils.isNotBlank(group.getEx()) ? JSONObject.parseObject(group.getEx())
                : JSONObject.parseObject("{}");
        extObj.put(EX_CUSTOMER_SERVICE_STATUS, customerServiceStatus.getCode());
        extObj.put(EX_OP_TYPE, opType.getCode());
        if (StringUtils.isNotBlank(opUserId)) {
            Map<String, Object> opUserMap = new HashMap<>();
            opUserMap.put(EX_OP_USER_ID, opUserId);
            opUserMap.put(EX_OP_USER_NICKNAME, opNickname);
            extObj.put(EX_OP_USER, opUserMap);
        }

        String exStr = extObj.toJSONString();
        group.setEx(exStr);
        // 更新群简介，避免app收到回调时ex字段不更新问题
        group.setNotice(String.valueOf(System.currentTimeMillis()));

        groupService.update(Wrappers.<GroupDO>lambdaUpdate()
                .set(GroupDO::getEx, exStr)
                .set(GroupDO::getGmtModified, new Date())
                .set(GroupDO::getNotice, group.getNotice())
                .eq(GroupDO::getId, groupId));
    }

    @Override
    public void handleGroupApply(HandleApplyReqDTO reqDTO) throws PendingException {
        GroupApplyBO apply = this
                .getOne(Wrappers.<GroupApplyBO>lambdaQuery().eq(GroupApplyBO::getId, reqDTO.getApplyId()));
        if (apply == null) {
            throw new PendingException(ImCodeEnum.I1018);
        }

        if (apply.getStatus() != GroupApplyStatusEnum.PROCESSING.getCode()) {
            throw new PendingException(ImCodeEnum.I1028);
        }

        // 处理客服
        UserOperatorInfoForImDTO userOperatorInfoForImDTO = userOperatorFacade
                .getOperatorInfoForIM(reqDTO.getOperatorNo());

        // 受理客服退群
        log.info("开始踢用户出群handle, applyNo:{}", reqDTO.getApplyId());
        groupMemberService.kick(apply.getGroupId(), userOperatorInfoForImDTO.getOperatorNo());
        log.info("结束踢用户出群handle, applyNo:{}", reqDTO.getApplyId());

        // 修改群信息
        this.updateGroupInfo(apply.getGroupId(), GroupCustomerServiceStatusEnum.NOT_APPLY,
                GroupInfoOpTypeEnum.FINISH_CUSTOMER_SERVICE,
                userOperatorInfoForImDTO.getOperatorNo(), userOperatorInfoForImDTO.getNickName());

        // 更新状态
        apply.setStatus(GroupApplyStatusEnum.HANDLED.getCode());
        apply.setHandleResult(GroupApplyHandleResultEnum.getByCode(reqDTO.getHandleResult()));
        apply.setRemark(reqDTO.getRemark());
        apply.setHandleOperatorNo(reqDTO.getOperatorNo());
        apply.setHandleOperatorName(userOperatorInfoForImDTO == null ? null : userOperatorInfoForImDTO.getNickName());
        apply.setHandleTime(new Date());
        this.updateById(apply);
    }

    @Override
    public GroupApplyCountRespDTO countGroupApply(GroupApplyCountReqDTO reqDTO) throws PendingException {
        long pendingCount = this.count(Wrappers.<GroupApplyBO>lambdaQuery().eq(GroupApplyBO::getStatus,
                GroupApplyStatusEnum.PENDING.getCode()));
        long processingCount = this.count(Wrappers.<GroupApplyBO>lambdaQuery().eq(GroupApplyBO::getStatus,
                GroupApplyStatusEnum.PROCESSING.getCode()));
        return new GroupApplyCountRespDTO().setPendingCount(pendingCount).setProcessingCount(processingCount);
    }

    @Override
    public PageInfoDTO<GroupApplyRespDTO> pageGroupApply(GroupApplySearchReqDTO reqDTO) throws PendingException {
        IPage<GroupApplyBO> page = this.page(new Page<GroupApplyBO>(reqDTO.getPageNum(), reqDTO.getPageSize()),
                Wrappers.<GroupApplyBO>lambdaQuery()
                        .orderByDesc(GroupApplyBO::getApplyTime)
                        .eq(GroupApplyBO::getDelState, DelStateEnum.NORMAL)
                        .eq(StringUtils.isNotBlank(reqDTO.getBusinessLine()), GroupApplyBO::getBusinessLine,
                                reqDTO.getBusinessLine())
                        .eq(StringUtils.isNotBlank(reqDTO.getApplyNo()), GroupApplyBO::getId, reqDTO.getApplyNo())
                        .eq(StringUtils.isNotBlank(reqDTO.getApplyScene()), GroupApplyBO::getApplyScene,
                                reqDTO.getApplyScene())
                        .eq(StringUtils.isNotBlank(reqDTO.getBusinessLine()), GroupApplyBO::getBusinessLine,
                                reqDTO.getBusinessLine())
                        .eq(StringUtils.isNotBlank(reqDTO.getGroupId()), GroupApplyBO::getGroupId, reqDTO.getGroupId())
                        .eq(StringUtils.isNotBlank(reqDTO.getOrderNo()), GroupApplyBO::getOrderNo, reqDTO.getOrderNo())
                        .eq(StringUtils.isNotBlank(reqDTO.getLanguage()), GroupApplyBO::getLanguage,
                                reqDTO.getLanguage())
                        .like(StringUtils.isNotBlank(reqDTO.getAcceptOperatorName()),
                                GroupApplyBO::getAcceptOperatorName, reqDTO.getAcceptOperatorName())
                        .like(StringUtils.isNotBlank(reqDTO.getHandleOperatorName()),
                                GroupApplyBO::getHandleOperatorName, reqDTO.getHandleOperatorName())
                        .eq(reqDTO.getOperatorType() != null, GroupApplyBO::getOperatorType, reqDTO.getOperatorType())
                        .eq(GroupApplyStatusEnum.getByCode(reqDTO.getStatus()) != null, GroupApplyBO::getStatus,
                                GroupApplyStatusEnum.getByCode(reqDTO.getStatus()))
                        .ge(reqDTO.getStartTime() != null, GroupApplyBO::getApplyTime, reqDTO.getStartTime())
                        .le(reqDTO.getEndTime() != null, GroupApplyBO::getApplyTime, reqDTO.getEndTime()));

        List<GroupApplyRespDTO> respList = page.getRecords().stream()
                .map(GroupApplyServiceImpl::change2GroupApplyResp)
                .collect(Collectors.toList());

        return PageUtil.createPage(page, respList);
    }

    private static GroupApplyRespDTO change2GroupApplyResp(GroupApplyBO a) {
        return new GroupApplyRespDTO()
                .setApplyId(a.getId())
                .setApplyTime(a.getApplyTime())
                .setApplyScene(a.getApplyScene())
                .setStatus(a.getStatus())
                .setReason(a.getReason())
                .setGroupId(a.getReason())
                .setBusinessLine(a.getBusinessLine())
                .setOperatorNo(a.getOperatorNo())
                .setOperatorType(a.getOperatorType())
                .setNickName(a.getNickname())
                .setStoreName(a.getStoreName())
                .setOrderNo(a.getOrderNo())
                .setLanguage(a.getLanguage())
                .setAcceptOperatorNo(a.getAcceptOperatorNo())
                .setAcceptOperatorName(a.getAcceptOperatorName())
                .setAcceptTime(a.getAcceptTime())
                .setHandleOperatorNo(a.getHandleOperatorNo())
                .setHandleOperatorName(a.getHandleOperatorName())
                .setHandleTime(a.getHandleTime())
                .setHandleResult(a.getHandleResult() == null ? null : a.getHandleResult().getCode())
                .setRemark(a.getRemark());
    }

    @Override
    public GroupApplyRespDTO getGroupApplyDetail(GroupApplyDetailReqDTO reqDTO) throws PendingException {
        GroupApplyBO apply = this
                .getOne(Wrappers.<GroupApplyBO>lambdaQuery().eq(GroupApplyBO::getId, reqDTO.getApplyId()));
        return apply == null ? null : change2GroupApplyResp(apply);
    }

    @Override
    public void readGroupApply(ReadApplyReqDTO reqDTO) throws PendingException {
        LambdaUpdateWrapper<GroupApplyBO> wrapper = Wrappers.<GroupApplyBO>lambdaUpdate()
                .set(GroupApplyBO::getReadStatus, GroupApplyReadStatusEnum.READ.getCode())
                .set(GroupApplyBO::getUpdateTime, new Date())
                .eq(GroupApplyBO::getId, reqDTO.getApplyId());
        this.update(wrapper);
        throw new UnsupportedOperationException("Unimplemented method 'readGroupApply'");
    }

}
