package com.chaos.im.c2g.group.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chaos.im.c2g.group.dao.GroupDAO;
import com.chaos.im.c2g.group.domain.GroupDO;
import com.chaos.im.c2g.group.domain.GroupQuery;
import com.chaos.im.c2g.group.mapper.GroupMapper;
import com.chaos.keep.alive.common.core.domain.BasePage;
import com.chaos.keep.alive.common.web.dao.MybatisPlusDAOImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public class GroupDAOImpl extends MybatisPlusDAOImpl<GroupMapper, GroupDO> implements GroupDAO {

    @Override
    public BasePage<GroupDO> listByPage(GroupQuery query) {
        LambdaQueryWrapper<GroupDO> queryWrapper = new LambdaQueryWrapper<>();
        if(StringUtils.isNotBlank(query.getName())){
            queryWrapper.likeLeft(GroupDO::getName, query.getName());
        }
        return listPage(query, queryWrapper);
    }
}
