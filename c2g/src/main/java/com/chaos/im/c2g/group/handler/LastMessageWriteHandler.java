package com.chaos.im.c2g.group.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.chaos.im.c2g.group.domain.C2gMessageBO;
import com.chaos.keep.alive.common.im.constant.ImConstants;
import com.chaos.keep.alive.common.web.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;

import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class LastMessageWriteHandler extends MessageWriteHandler {


    private RedissonClient redissonClient;

    private volatile boolean initialized = false;

    private void initDependencies() {
        if (!initialized && SpringContextUtils.getApplicationContext() != null) {
            synchronized (this) {
                if (!initialized) {
                    try {
                        this.redissonClient = SpringContextUtils.getBean(RedissonClient.class);
                        this.initialized = true;
                        log.info("LastMessageWriteHandler dependencies initialized successfully");
                    } catch (Exception e) {
                        log.error("Failed to initialize dependencies", e);
                    }
                }
            }
        }
    }

    @Override
    public boolean isSupport(Long chatId, C2gMessageBO c2gMessageBO) {

        Map<String, Object> map = BeanUtil.beanToMap(c2gMessageBO);
        log.debug("<UNK>{}", JSONUtil.toJsonStr(map));
        return map != null;
    }

    @Override
    public void handleRequest(Long chatId, C2gMessageBO c2gMessageBO) {
        initDependencies();
        log.debug("enter lastMessageWriteHandler handleRequest");

        String lastMessageKey = ImConstants.C2G_LAST_MESSAGE_PREFIX + chatId;

        Map<String, Object> map = BeanUtil.beanToMap(c2gMessageBO);

        Map<String, Object> filteredMap = map.entrySet().stream()
                .filter(entry -> entry.getValue() != null)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue
                ));
        log.info("记录最后一条c2gMessageBo:{}", JSONUtil.toJsonStr(filteredMap));
        redissonClient.getMap(lastMessageKey).putAll(filteredMap);
    }
}
