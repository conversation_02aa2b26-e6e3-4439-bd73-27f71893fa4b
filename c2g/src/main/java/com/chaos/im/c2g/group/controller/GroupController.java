package com.chaos.im.c2g.group.controller;

import cn.hutool.core.util.ObjectUtil;
import com.chaos.im.c2g.group.api.GroupApi;
import com.chaos.im.c2g.group.api.domain.*;
import com.chaos.im.c2g.group.domain.*;
import com.chaos.im.c2g.group.service.GroupService;
import com.chaos.im.c2g.group.service.MessageSendService;
import com.chaos.im.chat.api.ChatApi;
import com.chaos.keep.alive.common.core.util.BeanCopierUtils;
import com.outstanding.framework.core.ResponseDTO;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/group")
public class GroupController {

    @Autowired
    private GroupService groupService;

    @DubboReference(version = "1.0.0")
    private Chat<PERSON>pi chatApi;

    @DubboReference(version = "1.0.0")
    private GroupApi groupApi;

    @Resource
    private MessageSendService messageSendService;


    @PostMapping("/create")
    public ResponseDTO<?> create(@RequestBody GroupVO groupVO) {
        Long groupId = groupService.create(groupVO.clone(GroupDTO.class));
        groupVO.setId(groupId);
        return ResponseDTO.creatDTO(groupVO);
    }

    @PostMapping("/createAndInvite")
    public ResponseDTO<?> createAndInvite(@RequestBody CreateGroupAndInviteReq req) {
        groupService.createAndInvite(req.getGroup(), req.getMemberOperatorNos());
        return ResponseDTO.creatDTO(req.getGroup());
    }

    @PostMapping("/list")
    public ResponseDTO<?> listByPage(GroupQuery query) {
        return ResponseDTO.creatDTO(BeanCopierUtils.convert(groupService.listByPage(query), GroupVO.class));
    }


    @GetMapping("/find")
    public ResponseDTO<?> findByGroupId(@RequestParam("groupId") Long groupId) {
        GroupDO groupDO = groupService.findByGroupId(groupId);
        GroupDTO groupDTO = new GroupDTO();
        Long chatId = chatApi.getChatIdByGroupId(groupDO.getId());

        BeanCopierUtils.copy(groupDO, groupDTO);

        groupDTO.setChatId(chatId);
        return ResponseDTO.creatDTO(groupDTO);
    }

    @PostMapping("/lastMessage")
    public ResponseDTO<?> getLastMessageByGroupIdAndOperatorNo(@RequestBody GetLastMessageReq req) {
        if(req.getChatId() == null && req.getOperatorNo() == null) {
            throw new RuntimeException("chatId or groupId can't be null");
        }
        LastMessageVO lastMessageVO = new LastMessageVO();
        if (req.getGroupId() != null) {
            Long chatId = chatApi.getChatIdByGroupId(req.getGroupId());
            LastC2gMessageDTO lastC2gMessageDTO = messageSendService.getLastMessageByChatIdAndOperatorNo(chatId,
                    req.getOperatorNo(), req.getDeviceId());
            if (ObjectUtil.isNotNull(lastC2gMessageDTO)) {
                BeanCopierUtils.copy(lastC2gMessageDTO, lastMessageVO);
            }
            lastMessageVO.setChatId(chatId);
            lastMessageVO.setGroupId(req.getGroupId());
            return ResponseDTO.creatDTO(lastMessageVO);
        }
        if (req.getChatId() != null) {
            LastC2gMessageDTO lastC2gMessageDTO = messageSendService.getLastMessageByChatIdAndOperatorNo(req.getChatId(),
                    req.getOperatorNo(), req.getDeviceId());
            if (ObjectUtil.isNotNull(lastC2gMessageDTO)) {
                BeanCopierUtils.copy(lastC2gMessageDTO, lastMessageVO);
            }
            String groupId = chatApi.getPeerIdByChatId(req.getChatId());
            lastMessageVO.setChatId(req.getChatId());
            lastMessageVO.setGroupId(Long.parseLong(groupId));
            return ResponseDTO.creatDTO(lastMessageVO);
        }
        return ResponseDTO.creatDTO(lastMessageVO);
    }

    @GetMapping("/findByChatId")
    public ResponseDTO<?> findByChatId(@RequestParam("chatId") Long chatId) {

        return ResponseDTO.creatDTO(groupService.findByChatId(chatId));

    }

    @DeleteMapping("/dismiss")
    public ResponseDTO<?> dismiss(@RequestParam Long groupId) {
        groupService.dismiss(groupId);
        return ResponseDTO.creatDTO();
    }


}
