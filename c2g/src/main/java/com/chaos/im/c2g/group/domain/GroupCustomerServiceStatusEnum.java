package com.chaos.im.c2g.group.domain;

import com.outstanding.framework.core.BaseEnum;

public enum GroupCustomerServiceStatusEnum implements BaseEnum<GroupCustomerServiceStatusEnum, Integer> {
    NOT_APPLY(10, "未申请客服加入"),
    WAIT_JOIN(11, "等待客服加入"),
    HAS_JOIN(12, "客服已加入");

    GroupCustomerServiceStatusEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    private Integer code;
    private String message;

    public Integer getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }
}
