package com.chaos.im.c2g.group.controller;

import com.chaos.im.c2g.group.api.domain.GroupApplyDetailReqDTO;
import com.chaos.im.c2g.group.api.domain.GroupApplyRespDTO;
import com.chaos.im.c2g.group.api.domain.GroupApplySearchReqDTO;
import com.chaos.im.c2g.group.domain.AcceptApplyReqDTO;
import com.chaos.im.c2g.group.domain.ApplyCustomerServiceReqDTO;
import com.chaos.im.c2g.group.domain.HandleApplyReqDTO;
import com.chaos.im.c2g.group.service.GroupApplyService;
import com.outstanding.framework.core.PageInfoDTO;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.core.ResponseDTO;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/group/apply")
public class GroupApplyController {

    @Autowired
    private GroupApplyService groupApplyService;

//    @PostMapping("/customer")
//    public ResponseDTO<?> applyCustomerService(@RequestBody ApplyCustomerServiceReqDTO reqDTO) {
//        groupApplyService.applyCustomerService(reqDTO);
//        return ResponseDTO.creatDTO();
//    }
//    @PostMapping("/accept")
//    public ResponseDTO<?> acceptGroupApply(@RequestBody AcceptApplyReqDTO reqDTO) {
//        groupApplyService.acceptGroupApply(reqDTO);
//        return ResponseDTO.creatDTO();
//    }
//
//    @PostMapping("/handle")
//    public ResponseDTO<?> handleGroupApply(@RequestBody HandleApplyReqDTO reqDTO) {
//        groupApplyService.handleGroupApply(reqDTO);
//        return ResponseDTO.creatDTO();
//    }
//
//    @PostMapping("/page")
//    public PageInfoDTO<GroupApplyRespDTO> page(@RequestBody GroupApplySearchReqDTO body){
//       return groupApplyService.pageGroupApply(body);
//    }
//
//    /**
//     * 查询客服介入申请详情
//     */
//    @PostMapping("/detail")
//    public GroupApplyRespDTO detail(@RequestBody @Valid GroupApplyDetailReqDTO reqDTO) throws PendingException {
//        return groupApplyService.getGroupApplyDetail(reqDTO);
//    }
}
