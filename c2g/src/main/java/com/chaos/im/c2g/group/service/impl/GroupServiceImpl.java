package com.chaos.im.c2g.group.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaos.im.c2g.group.api.domain.GroupDTO;
import com.chaos.im.c2g.group.api.domain.GroupStatusEnum;
import com.chaos.im.c2g.group.dao.GroupDAO;
import com.chaos.im.c2g.group.domain.*;
import com.chaos.im.c2g.group.mapper.GroupMapper;
import com.chaos.im.c2g.group.service.GroupMemberService;
import com.chaos.im.c2g.group.service.GroupService;
import com.chaos.im.chat.api.ChatApi;
import com.chaos.keep.alive.common.core.domain.BasePage;
import com.chaos.keep.alive.common.core.util.BeanCopierUtils;
import com.chaos.keep.alive.common.core.util.ResultHelper;
import com.chaos.keep.alive.common.im.constant.Constants;
import com.outstanding.framework.core.ResponseDTO;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class GroupServiceImpl extends ServiceImpl<GroupMapper, GroupDO> implements GroupService {

    @Autowired
    private GroupMemberService groupMemberService;

    @Autowired
    private GroupDAO groupDAO;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @DubboReference(version = "1.0.0")
    private ChatApi chatApi;

    private static final String EX_CUSTOMER_SERVICE_STATUS = "customerServiceStatus";

    @Override
    public BasePage<GroupDTO> listByPage(GroupQuery query) {
        return BeanCopierUtils.convert(groupDAO.listByPage(query), GroupDTO.class);
    }

    @Override
    @Transactional
    public Long create(GroupDTO groupDTO) {
        groupDTO.setStatus(GroupStatusEnum.NORMAL.getCode());
        GroupDO groupDO = groupDTO.clone(GroupDO.class);
        groupDO.setStatus(groupDTO.getStatus());
        Long groupId = groupDAO.save(groupDO);
        groupDTO.setId(groupId);
        groupMemberService.join(groupDTO);
        return groupId;
    }

    @Override
    @Transactional
    public void dismiss(Long groupId) {
        groupDAO.remove(groupId);
        redisTemplate.delete(GroupMemberServiceImpl.GROUP_KEY_PREFIX + "::" +groupId);
        groupMemberService.dismiss(groupId);
        chatApi.deleteChatByGroupId(Constants.CHAT_TYPE_C2G, groupId);
    }

    @Override
    public GroupDO findByGroupId(Long groupId) {
       return getById(groupId);
    }

    @Override
    public GroupDO findByChatId(Long chatId) {

       String peerId =  chatApi.getPeerIdByChatId(chatId);

      return findByGroupId(Long.parseLong(peerId));

    }

    @Override
    public GroupDO queryByOrderNo(String ownerId, String orderNo, Integer bizType){
        LambdaQueryWrapper<GroupDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GroupDO::getOwnerId,ownerId);
        List<GroupDO> groupDOS = list(queryWrapper);
        List<GroupDO> resList = groupDOS.stream().filter(groupDO -> {
           String exStr = groupDO.getEx();
           Map<String,Object> exMap = JSONObject.parseObject(exStr);
           if(!exMap.containsKey("orderNo")){
               return false;
           }
           if(!exMap.containsKey("bizType")){
               return false;
           }

           String ord =  (String) exMap.get("orderNo");
            Integer type= (Integer) exMap.get("bizType");
            return orderNo.equals(ord) && bizType.equals(type);
        }).collect(Collectors.toList());

        if(resList.isEmpty()){
            return null;
        }
        return resList.get(0);
    }

    @Transactional
    @Override
    public ResponseDTO<Long> createAndInvite(GroupDTO groupDTO,List<String> memberOperatorNos) {

        JSONObject exObj = JSONObject.parseObject(groupDTO.getEx());
        //添加客服状态
        String exStr = null;
        if(!Objects.isNull(exObj)) {
            exObj.put(EX_CUSTOMER_SERVICE_STATUS, GroupCustomerServiceStatusEnum.NOT_APPLY.getCode());
            exStr = exObj.toJSONString();
        }

        groupDTO.setEx(exStr);
        Long groupId =  this.create(groupDTO);

        for(String memberOperatorNo : memberOperatorNos){
            groupMemberService.join(groupId, memberOperatorNo);
        }
        return ResultHelper.ok(groupId);
    }
}
