package com.chaos.im.c2g.group.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

import org.apache.ibatis.type.EnumTypeHandler;

/**
 * <p>
 * IM客服进群申请
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-29
 */
@Getter
@Setter
@TableName("GROUP_APPLY")
public class GroupApplyBO extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 申请时间
     */
    private Date applyTime;

    /**
     * 申请场景
     */
    private String applyScene;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 申请原因
     */
    private String reason;

    /**
     * 群id
     */
    private Long groupId;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 申请人操作员编号
     */
    private String operatorNo;

    /**
     * 申请人操作员类型
     */
    private Integer operatorType;

    /**
     * 申请人昵称
     */
    private String nickname;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 语言
     */
    private String language;

    /**
     * 受理人操作员编号
     */
    private String acceptOperatorNo;

    /**
     * 受理人名称
     */
    private String acceptOperatorName;

    /**
     * 受理时间
     */
    private Date acceptTime;

    /**
     * 处理人操作员编号
     */
    private String handleOperatorNo;

    /**
     * 处理人名称
     */
    private String handleOperatorName;

    /**
     * 处理时间
     */
    private Date handleTime;

    /**
     * 处理结果：10-已解决、11-未解决
     */
    private GroupApplyHandleResultEnum handleResult;

    /**
     * 备注
     */
    private String remark;

    /**
     * 最后会话时间
     */
    private Date lastConversationTime;

    /**
     * 群主操作员编号
     */
    private String groupOperatorNo;

    /**
     * 群主手机号
     */
    private String groupMobile;

    /**
     * 阅读状态：10-已读、11-未读
     */
    private Integer readStatus;
}
