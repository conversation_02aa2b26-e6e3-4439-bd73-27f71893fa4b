package com.chaos.im.c2g.group.domain;

import com.chaos.keep.alive.common.core.domain.BaseDomain;
import lombok.Getter;
import lombok.Setter;


/**
 * <AUTHOR>
 */
@Getter
@Setter
public class GroupMemberVO extends BaseDomain {

    private Long id;

    /**
     * 群组id
     */
    private Long groupId;

    /**
     * 账户id
     */
    private String memberId;

    private String nickname;

    private String avatar;
}