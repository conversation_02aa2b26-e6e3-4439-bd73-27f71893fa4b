package com.chaos.im.c2g.group.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = false)
@Data
@Document(collection = "c2g_message")
public class C2gMessageBO implements Serializable {

    @Id
    private Long id;
    private Long chatId;
    private String messageId;
    private String fromOperatorNo;
    private Integer category;
    private String content;
    private Long groupId;
    private String appId;
    private Long sequence;
    private Long timestamp;

}
