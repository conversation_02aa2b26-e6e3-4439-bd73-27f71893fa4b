package com.chaos.im.c2g.group.domain;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class AcceptApplyReqDTO implements Serializable {
    private static final long serialVersionUID = 7679300814297508726L;

    /**
     * 申请单编号
     */
    @NotBlank(message = "申请单编号不能为空")
    private Long applyId;

    /**
     * 客服操作员编号
     */
    @NotBlank(message = "操作员编号不能为空")
    private String operatorNo;
}
