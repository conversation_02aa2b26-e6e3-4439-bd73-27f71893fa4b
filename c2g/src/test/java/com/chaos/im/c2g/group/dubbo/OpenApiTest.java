package com.chaos.im.c2g.group.dubbo;

import java.util.LinkedList;
import java.util.List;

import com.chaos.im.c2g.group.api.domain.CreateGroupAndInviteReq;
import com.chaos.im.c2g.group.api.domain.GroupDTO;
import com.chaos.im.c2g.group.service.GroupService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.outstanding.framework.core.ResponseDTO;

@RunWith(SpringRunner.class)
@SpringBootTest
public class OpenApiTest {

    @Autowired
    private GroupService groupService;

    @Test
    public void testCreateGroup(){
        List<String> members = new LinkedList<>();
        members.add("1278316867292663808");
        GroupDTO groupDTO = new GroupDTO();
        groupDTO.setOwnerId("1278310923422830592");
        groupDTO.setName("dubbo测试");
        ResponseDTO<Long> result =  groupService.createAndInvite(groupDTO,members);
        System.out.println("result:"+result.getData());
    }
    
}
