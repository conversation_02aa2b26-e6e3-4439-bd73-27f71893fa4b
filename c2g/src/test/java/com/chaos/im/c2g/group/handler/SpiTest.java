package com.chaos.im.c2g.group.handler;

import org.apache.dubbo.common.extension.ExtensionLoader;
import org.junit.Test;

public class SpiTest {

    @Test
    public void testSpiLoad() {
        try {
            ExtensionLoader<IMessageSendHandler> loader = ExtensionLoader.getExtensionLoader(IMessageSendHandler.class);
            
            System.out.println("Available extensions: " + loader.getSupportedExtensions());
            
            IMessageSendHandler ackHandler = loader.getExtension("ack");
            System.out.println("Ack handler loaded: " + ackHandler.getClass().getName());
            
            IMessageSendHandler pushHandler = loader.getExtension("push");
            System.out.println("Push handler loaded: " + pushHandler.getClass().getName());
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
