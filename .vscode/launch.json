{"configurations": [{"type": "java", "name": "ChatApplication", "request": "launch", "mainClass": "com.chaos.im.chat.ChatApplication", "projectName": "chat-service"}, {"type": "java", "name": "C2cApplication", "request": "launch", "mainClass": "com.chaos.im.c2c.C2cApplication", "projectName": "c2c-service"}, {"type": "java", "name": "C2gApplication", "request": "launch", "mainClass": "com.chaos.im.c2g.group.C2gApplication", "projectName": "c2g-service", "vmArgs": " -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=61963 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Dspring.jmx.enabled=true -Djava.rmi.server.hostname=localhost -Dspring.application.admin.enabled=true -Dspring.boot.project.name=c2g-service"}, {"type": "java", "name": "Main", "request": "launch", "mainClass": "com.chaos.keep.alive.client.Main", "projectName": "keep-alive-client-mock"}, {"type": "java", "name": "GatewayTcpApplication", "request": "launch", "mainClass": "com.lifekh.gateway.tcp.GatewayTcpApplication", "projectName": "gateway-tcp", "vmArgs": " -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=51529 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Dspring.jmx.enabled=true -Djava.rmi.server.hostname=localhost -Dspring.application.admin.enabled=true -Dspring.boot.project.name=gateway-tcp"}, {"type": "java", "name": "Spring Boot-KeepAliveRouteApplication<route>", "request": "launch", "cwd": "${workspaceFolder}", "mainClass": "com.chaos.route.KeepAliveRouteApplication", "projectName": "route", "args": "", "envFile": "${workspaceFolder}/.env", "vmArgs": " -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=51560 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Dspring.jmx.enabled=true -Djava.rmi.server.hostname=localhost -Dspring.application.admin.enabled=true -Dspring.boot.project.name=route"}, {"type": "java", "name": "Spring Boot-IdApplication<id-server>", "request": "launch", "cwd": "${workspaceFolder}", "mainClass": "com.chaos.keep.alive.im.id.server.IdApplication", "projectName": "id-server", "args": "", "envFile": "${workspaceFolder}/.env", "vmArgs": " -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=61868 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Dspring.jmx.enabled=true -Djava.rmi.server.hostname=localhost -Dspring.application.admin.enabled=true -Dspring.boot.project.name=id-server"}]}