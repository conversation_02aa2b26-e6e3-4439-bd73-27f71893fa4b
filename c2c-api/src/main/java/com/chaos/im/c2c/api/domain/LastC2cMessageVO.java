package com.chaos.im.c2c.api.domain;

import com.chaos.keep.alive.common.core.domain.BaseDomain;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class LastC2cMessageVO extends BaseDomain implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long chatId;
    private Long messageId;
    private String fromOperatorNo;
    private String toOperatorNo;
    private Integer category;
    private String content;
    private String appId;
    private Long sequence;
    private Long timestamp;
//    private Long unReadCount;


}
