package com.chaos.keep.alive.im.id.server.service;

import com.netease.nim.camellia.id.gen.segment.CamelliaSegmentIdGen;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class IdServerService {

    private static final Logger logger = LoggerFactory.getLogger(IdServerService.class);

    @Autowired
    private CamelliaSegmentIdGen camelliaSegmentIdGen;

    public Long genId(String tag) {
        long id = this.camelliaSegmentIdGen.genId(tag);
        if (logger.isDebugEnabled()) {
            logger.debug("genId, tag = {}, id = {}", tag, id);
        }
        return id;

    }
}
