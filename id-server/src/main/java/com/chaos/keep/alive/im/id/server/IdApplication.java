package com.chaos.keep.alive.im.id.server;


import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@ComponentScan(basePackages = {"com.netease.nim.camellia.id.gen.springboot.segment",
        "com.netease.nim.camellia.id.gen.springboot.idloader",
        "com.chaos.keep.alive.im.id.server"})
@MapperScan("com.netease.nim.camellia.id.gen.springboot.idloader")
public class IdApplication {

    public static void main(String[] args) {
        SpringApplication.run(IdApplication.class, args);
    }
}
