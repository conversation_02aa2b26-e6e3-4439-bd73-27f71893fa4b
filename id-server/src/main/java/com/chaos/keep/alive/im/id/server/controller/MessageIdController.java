package com.chaos.keep.alive.im.id.server.controller;

import com.netease.nim.camellia.id.gen.common.CamelliaIdGenException;
import com.netease.nim.camellia.id.gen.segment.CamelliaSegmentIdGen;
import com.netease.nim.camellia.id.gen.springboot.segment.IdSyncInMultiRegionService;
import com.outstanding.framework.core.ResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/generate")
public class MessageIdController {


    private static final Logger logger = LoggerFactory.getLogger(MessageIdController.class);
    @Autowired
    private CamelliaSegmentIdGen camelliaSegmentIdGen;
    @Autowired
    private IdSyncInMultiRegionService idSyncInMultiRegionService;

    public MessageIdController() {
    }

    @GetMapping({"/ids"})
    public ResponseDTO<?> genIds(@RequestParam("tag") String tag, @RequestParam("count") int count) {
        try {
            List<Long> ids = this.camelliaSegmentIdGen.genIds(tag, count);
            if (logger.isDebugEnabled()) {
                logger.debug("genIds, tag = {}, count = {}, ids = {}", new Object[]{tag, count, ids});
            }

            return ResponseDTO.creatDTO(ids);
        } catch (CamelliaIdGenException var4) {
            CamelliaIdGenException e = var4;
            logger.error(e.getMessage(), e);
            ResponseDTO responseDTO = new ResponseDTO();
            responseDTO.setRspInf(e.getMessage());
            responseDTO.setRspCd("500");
            return responseDTO;
        } catch (Exception var5) {
            Exception e = var5;
            logger.error(e.getMessage(), e);
            ResponseDTO responseDTO = new ResponseDTO();
            responseDTO.setRspInf(e.getMessage());
            responseDTO.setRspCd("500");
            return responseDTO;
        }
    }

    @GetMapping({"/id"})
    public ResponseDTO<?> genId(@RequestParam("tag") String tag) {
        try {
            long id = this.camelliaSegmentIdGen.genId(tag);
            if (logger.isDebugEnabled()) {
                logger.debug("genId, tag = {}, id = {}", tag, id);
            }

            return ResponseDTO.creatDTO(id);
        } catch (CamelliaIdGenException var4) {
            CamelliaIdGenException e = var4;
            logger.error(e.getMessage(), e);
            ResponseDTO responseDTO = new ResponseDTO();
            responseDTO.setRspInf(e.getMessage());
            responseDTO.setRspCd("500");
            return responseDTO;
        } catch (Exception var5) {
            Exception e = var5;
            logger.error(e.getMessage(), e);
            ResponseDTO responseDTO = new ResponseDTO();
            responseDTO.setRspInf("internal error");
            responseDTO.setRspCd("500");
            return responseDTO;
        }
    }
}
