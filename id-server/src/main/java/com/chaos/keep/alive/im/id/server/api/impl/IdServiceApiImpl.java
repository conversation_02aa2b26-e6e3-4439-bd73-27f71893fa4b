package com.chaos.keep.alive.im.id.server.api.impl;

import com.chaos.keep.alive.im.id.server.api.IdServerApi;
import com.chaos.keep.alive.im.id.server.service.IdServerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class IdServiceApiImpl implements IdServerApi {

    @Autowired
    private IdServerService idServerService;

    @Override
    public Long genId(String tag) {

        return idServerService.genId(tag);
    }
}
