camellia-id-gen-segment:
    max-retry: 100
    region-bits: 0
    region-id: 0
    region-id-shifting-bits: 0
    retry-interval-millis: 10
    step: 1000
    tag-count: 1000
server:
    servlet:
        context-path: /id-server
spring:
    application:
        name: id-server
    datasource:
        asyncInit: true
        driver-class-name: com.mysql.cj.jdbc.Driver
        filters: stat
        initialSize: 50
        maxActive: 200
        maxOpenPreparedStatements: 20
        maxWait: 60000
        minEvictableIdleTimeMillis: 300000
        minIdle: 1
        password: wownowim_sit_2025
        poolPreparedStatements: true
        testOnBorrow: false
        testOnReturn: false
        testWhileIdle: true
        timeBetweenEvictionRunsMillis: 60000
        url: ***************************************************************************************************************************************************************
        username: wownowim_sit
    main:
        allow-bean-definition-overriding: true
        banner-mode: 'off'
